import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CreatePartnerTransactionForm, Partner, PartnerBand, PartnerTranactionDialogData, PartnerTransaction, UpdatePartnerTransactionForm } from '../../../../models/partner';
import { PartnerService } from '../../../../services/partner.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Mainaction } from '../../../../models/mainaction.model';

@Component({
  selector: 'app-partner-transation-dialog',
  standalone: false,
  templateUrl: './partner-transation-dialog.component.html',
  styleUrl: './partner-transation-dialog.component.css'
})
export class PartnerTransationDialogComponent implements OnInit {
  form!: FormGroup;
  isEditMode = false;
  salesId?: number;
  loading = false;
  saving = false;

 // products: Product[] = [];
  partners: Partner[] = [];
  partnerbands: PartnerBand[] = [];
  actionDetails: Mainaction[] = [];
  
  /// عرض الصورة الاساسية
  imagePathUrl: string | null = null;
  selectedFile: File | null = null;  //اختيار صورة جديدة
   imagePreview: string | null = null; //عرض الصورة اللي مختارة

  constructor(
    private fb: FormBuilder,
    private partnerService: PartnerService,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<PartnerTranactionDialogData>,
    @Inject(MAT_DIALOG_DATA) public data: PartnerTranactionDialogData
  ) {
    this.isEditMode = data.mode === 'edit';
    this.form = this.createForm();
    this.loadData();
  }

 ngOnInit(): void {
     if (this.isEditMode && this.data.partnertransaction) {
      this.populateForm(this.data.partnertransaction);
      this.imagePathUrl = this.data.partnertransaction.imagePath;
  }
}

loadData(): void {
  // Load Partners
    this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.partners = response.data;

        }
      },
      error: (error) => {
        console.error('Error loading partenrTransaction:', error);
      }
    });
    
    // Load PartnerBands

   const isGetDeleted = this.isEditMode ? true : false;

    this.partnerService.getPartnerBands(isGetDeleted).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.partnerbands = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading partenrTransaction:', error);
      }
    });
    // Load Partners
    this.partnerService.getMainActions(1).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.actionDetails = response.data;
        }
      },
      error: (error) => {
       
      }
    });
}

   private createForm(): FormGroup {
    return this.fb.group({
      transactionDate: [new Date(), Validators.required],
      actionDetailId: ['', Validators.required],
      partnerId: ['', Validators.required],
      partnerBandId: ['', Validators.required],
      amount: ['', [Validators.required, Validators.min(0)]],
      description: ['', [Validators.maxLength(100)]],
      notes: ['', [Validators.maxLength(100)]],
      imagePath: [null]
    });
  }
 private populateForm(partnertransaction: PartnerTransaction): void {
    this.form.patchValue({
      transactionDate: partnertransaction.transactionDate,
      actionDetailId: partnertransaction.actionDetailId,
      partnerId: partnertransaction.partnerId,
      partnerBandId: partnertransaction.partnerBandId,
      amount: partnertransaction.amount,
      description : partnertransaction.description,
      notes: partnertransaction.notes,
      imagePath: partnertransaction.imagePath
     
    });
  }

 onSubmit(): void {
    if (this.form.valid) {
      this.loading = true;
      const formValue = this.form.value;
      if (this.selectedFile) {
        formValue.imagePath = this.selectedFile;
      }
      if (this.isEditMode) {
        const updateDto: UpdatePartnerTransactionForm = formValue;
        this.partnerService.updatePartnerTransaction(this.data.partnertransaction!.id, updateDto)
          .subscribe({
            next: (response) => {
              if (response.succeeded && response.data != null) {
                this.dialogRef.close(true);
              }
              this.loading = false;
            },
            error: (error) => {
                this.loading = false;
            }
          });
      } else {
        const createDto: CreatePartnerTransactionForm = formValue;
        this.partnerService.createPartnerTransaction(createDto)
          .subscribe({
            next: (response) => {
              if (response.succeeded && response.data != null) {
                this.snackBar.open('تم إنشاء حركات الشريك بنجاح', 'إغلاق', { duration: 3000 });
                this.dialogRef.close(true);
              }
              this.loading = false;
            },
            error: (error) => {
             
              this.loading = false;
            }
          });
      }
    }
  }




onCancel(): void {
    this.dialogRef.close(false);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field?.hasError('required')) {
      return 'هذا الحقل مطلوب';
    }

    if (field?.hasError('maxlength')) {
      const maxLength = field.errors?.['maxlength']?.requiredLength;
      return `الحد الأقصى ${maxLength} حرف`;
    }
    if (field?.hasError('min')) {
      return 'يجب أن تكون القيمة أكبر من أو تساوي 0';
    }
    if (field?.hasError('max')) {
      return 'يجب أن تكون القيمة أقل من أو تساوي 100';
    }
    return '';
  }


 onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type ".jpg", ".jpeg", ".png", ".gif", ".bmp"
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'];
      if (!allowedTypes.includes(file.type)) {
        this.snackBar.open('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)', 'إغلاق', { duration: 4000 });
        return;
      }

      // Validate file size (5MB max)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        this.snackBar.open('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'إغلاق', { duration: 4000 });
        return;
      }

      this.selectedFile = file;

      // Create image preview
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.imagePreview = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }




}
