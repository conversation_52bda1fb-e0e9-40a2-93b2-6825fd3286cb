import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';
@Injectable({
  providedIn: 'root'
})
export class HandleErrorService {

constructor(private snackBar: MatSnackBar) { }

   logErrorResponse(errorResponse: HttpErrorResponse): Observable<any>   {
     // Client-side errors
     if (errorResponse.status === 0 || (typeof ErrorEvent !== 'undefined' && errorResponse.error instanceof ErrorEvent)) {
      // console.error('Client-side error:', errorResponse.error.message);
        this.showError('حدث خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت الخاص بك.');
      } else {
       // Server-side errors
              const errorMessage = errorResponse.error?.message || errorResponse.message || 'خطأ غير معروف';
            switch (errorResponse.status) {
                    case 400:
                      this.showError(errorMessage  || 'طلب غير صالح');
                      break;
                    case 401:
                     // errorMessage = 'غير مصرح بالوصول، يرجى تسجيل الدخول';
                      // يمكنك توجيه المستخدم لصفحة تسجيل الدخول 
                      this.showError(errorMessage  || 'تسجيل الدخول مطلوب');
                      break;
                    case 403:
                      this.showError(errorMessage   || 'غير مسموح بالوصول لهذا ');
                    //errorMessage = 'غير مسموح بالوصول لهذا المورد';
                      break;
                    case 404:
                      this.showError(errorMessage  || 'المورد غير موجود');
                      break;
                    case 500:
                      this.showError(errorMessage  || 'حدث خطأ في الخادم');                
                      break;
                        default:
                      this.showError(`حدث خطأ: ${errorResponse.status}`);
                      break;
                  }           
     }
    return throwError(() => 
  new Error(`Error ${errorResponse.status}: ${errorResponse.message || 'Unknown error'}`)
);
  }
  
  showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'center',
      verticalPosition: 'top',
      panelClass: ['error-snackbar']
    });
  }

}
