 <div class="partenrs-container">
      <!-- Header -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <mat-icon>list</mat-icon>
           قائمة البنود
          </h1>
          <button mat-raised-button color="primary" class="create-button" (click)="createPartnerBand()">
            <mat-icon>add</mat-icon>
            إضافة بند جديد
          </button>
        </div>
      </div>

      <!-- partenrs Table -->
      <mat-card class="table-card">
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="dataSource"  matSort class="partners-table mat-elevation-z8">
              <!-- Name Column -->
              <ng-container  matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>اسـم البند</th>
                <td mat-cell  *matCellDef="let partner">
                 <div class="name-cell">
                <strong> {{ partner.name }} </strong>
                <div class="description" *ngIf="partner.description">
                  {{ partner.description }}
                </div>
              </div>
              </td>

              </ng-container>

              <!-- Description Column -->
              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>الوصـف</th>
                <td mat-cell *matCellDef="let partner">
                  {{ partner.description || '-' }}
                </td>
              </ng-container>

           
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>الإجـراءات</th>
                <td mat-cell *matCellDef="let partner">
                  <div class="actions-cell">
                    <button mat-icon-button color="primary" matTooltip="تعديل"  (click)="editPartnerBand(partner)">
                      <mat-icon>edit</mat-icon>
                     
                    </button>
                    <button mat-icon-button color="warn" matTooltip="حذف" (click)="deletePartnerband(partner)">
                      <mat-icon>delete</mat-icon>
                    </button>
                   
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <!-- Loading Spinner -->
            <div class="loading-container" *ngIf="loading">
              <mat-spinner diameter="50"></mat-spinner>
              <p class="loading-text">جاري تحميل البنود...</p>
            </div>

            <!-- No Data Message -->
            <div class="no-data-container" *ngIf="!loading && partnerBands.length === 0">
              <mat-icon class="no-data-icon">people</mat-icon>
              <h3 class="no-data-title">لا يوجد بنود</h3>
              <p class="no-data-message">لم يتم العثور على أي بند. قم بإضافة بند جديد للبدء.</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
 </div>
