import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from './layout/layout/layout.component';
import { loginGuard } from './guards/login.guard';
import { authGuard } from './guards/auth.guard';
import { UnauthorizedComponent } from './features/shared/components/unauthorized/unauthorized.component';
import { permissionGuard } from './guards/permission.guard';

const routes: Routes = [

 
  {
    path: 'auth',
    canActivate: [loginGuard], 
    loadChildren: () =>
    import('./features/auth/auth.module').then(m => m.AuthModule),
  },
  {
    path: '',
    component: LayoutComponent,
     children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
        
      },
      {
        path: 'dashboard',
         canActivate: [authGuard],  
          loadChildren: () =>
          import('./features/dashboard/dashboard.module').then(m => m.DashboardModule),
      },
      {
        path: 'users',
         canActivate: [authGuard],  
          loadChildren: () =>
          import('./features/users/users.module').then(m => m.UsersModule),
      },
      {
        path: 'profile',
        loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule)
      },
       {
        path: 'partners',
         canActivate: [authGuard ,permissionGuard] ,
         data: { permissions: ['Permissions.Partner.View'] },  
          loadChildren: () =>
          import('./features/partner/partner.module').then(m => m.PartnerModule),
      },
      {
        path: 'products',
         canActivate: [authGuard ,permissionGuard] ,
         data: { permissions: ['Permissions.Stores.View'] },  
          loadChildren: () =>
          import('./features/store/store.module').then(m => m.StoreModule),
      }
    ]
  },
   {
    path: 'unauthorized',
    component: UnauthorizedComponent 
   },
  {
    path: '**',
    redirectTo: 'auth/login'
  }


];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
