export interface Product {
  id: number 
  code: string
  barcode: string
  name: string
  description: string
  categoryId: number
  categoryName: string
  unitId: number
  unitName: string
  standardCost: number
  minimumStock: number
  maximumStock: number
  openingBalance: number
  balance: number
  sortOrder: number
  itemType: number
  imagePath: string
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  isDeleted: boolean
  isActive: boolean
}

export interface ProductReq { 
  categoryId: number | null;
  stockStatus: string | null;
  priceFrom: number | null;
  priceTo: number | null;
}

export interface ProductDialogData {
  mode: 'create' | 'edit';
  product?: Product;
}

export interface Category {
  parentCategoryId: number | null;
  parentCategoryName: string
  code: string
  symbol: string
  categoryTypeId: number
  imageUrl: any
  sortOrder: number
  level: number
  hasChildren: boolean
  children?: Children[]
  name: string
  description: string
  id: number
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
}

export interface Children {
  parentCategoryId: number
  parentCategoryName: string
  code: string
  symbol: string
  categoryTypeId: number
  imageUrl: any
  sortOrder: number
  level: number
  hasChildren: boolean
  children: any[]
  name: string
  description: string
  id: number
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
}


