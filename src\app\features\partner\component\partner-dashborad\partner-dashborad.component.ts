import { Component, OnInit } from '@angular/core';
import { ChartConfiguration, ChartType } from 'chart.js';
import { Partner, PartnerBalanseSummary } from '../../../../models/partner';
import { PartnerService } from '../../../../services/partner.service';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-partner-dashborad',
  standalone: false,
  templateUrl: './partner-dashborad.component.html',
  styleUrl: './partner-dashborad.component.css'
})
export class PartnerDashboradComponent implements OnInit {

selectedPartnerId?: number;
partnersList: Partner[] = [];
pdfBlobUrl?: string;
pdfSrc: any; // سيعمل كرابط آمن
partnerBalanseSummaryById? : PartnerBalanseSummary;
partners: PartnerBalanseSummary[] = [];

  // بيانات المخطط
  public pieChartType: ChartType = 'pie';
  public pieChartLabels: string[] = [];
  public pieChartDatasets: ChartConfiguration['data']['datasets'] = [
    {
      data: [],
      backgroundColor: [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#C9CBCF', '#7CFC00'
      ],
      hoverBackgroundColor: '#555',
      label: 'نسبة الأسهم'
    }
  ];
public pieChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const label = context.label || '';
            const value = context.parsed;
            return `${label}: ${value.toFixed(2)}%`;
          }
        }
      }
    }
  };
constructor(private partnerService: PartnerService,
  private sanitizer: DomSanitizer
) {} 


  ngOnInit(): void {
    this.getPartnerBalanseSummary();
    this.getPartnersList();
  }

  getPartnerBalanseSummary(): void {
    this.partnerService.getPartnersBalanseSummary().subscribe({
      next: (response) => {
         if (response.succeeded && response.data) {
          
        this.partners = response.data;

          // استخراج الأسماء والنسب
          this.pieChartLabels = this.partners.map(p => p.partnerName);
          this.pieChartDatasets[0].data = this.partners.map(p => p.sharePercentage);

          // تحديث المخطط
          this.pieChartDatasets = [...this.pieChartDatasets];
      }
    },     
      error: (error) => {
        console.error('Error loading partners balanse summary:', error);
      }
    });
  }

  
  getPartnersList(): void {
    this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.partnersList = response.data;

        }
      },
      error: (error) => {
        console.error('Error loading partners:', error);
      }
    });
  }

  getPartnerBalanseSummaryByPartnerId(partnerId: number): void {
    this.partnerService.getPartnersBalanseSummary().subscribe({
      next: (response) => {
         if (response.succeeded && response.data) {
          
        this.partnerBalanseSummaryById = response.data.find(p => p.partnerId === partnerId);

        
      }
    },     
      error: (error) => {
        console.error('Error loading partners balanse summary:', error);
      }
    });
  }


onFilterChange(partnerId: number): void {
  if (partnerId) {
    this.getPartnerBalanseSummaryByPartnerId(partnerId);
  } else {
    this.partnerBalanseSummaryById = undefined; // رجّع للوضع العام
    this.getPartnerBalanseSummary();
  }
}

exportToViews() { 
    const response = this.partnerService.getPartnerReportSummerViewUrl().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          window.open(response.data, '_blank');
        }
      },
      error: (error) => {
        console.error('Error exporting to PDF:', error);
       // this.snackBar.open('حدث خطأ أثناء عرض PDF', 'إغلاق', { duration: 3000 });
      }
    });    
  }


}
