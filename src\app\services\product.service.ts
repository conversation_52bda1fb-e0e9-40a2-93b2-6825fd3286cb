import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { ApiResponse, PagedResponse } from '../models/api-response.model';
import { Category, Product } from '../models/product.model';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class ProductService {

  constructor(private apiService: ApiService) {}

getCategories(): Observable<ApiResponse<Category[]>> {
  return this.apiService.get<Category[]>('Category');
}

  getProducts(paged: any): Observable<ApiResponse<PagedResponse<Product>>> {
  let param = new HttpParams();
   param = param.set('pageNumber', paged.pageNumber ?? 1)
   param = param.set('pageSize', paged.pageSize ?? 10)

   if(paged.searchTerm)
   param = param.set('searchTerm', paged.searchTerm || '') 

   if(paged.isActive != null)
    param = param.set('isActive', paged.isActive)

   if(paged.categoryId)
   param = param.set('categoryId', paged.categoryId)

   if(paged.stockStatus)
   param = param.set('stockStatus', paged.stockStatus)

        return this.apiService.get<PagedResponse<Product>>('Product',   {params:param} );

  }
   
}
