 <div class="partenrs-container">
      <!-- Header -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <mat-icon>people</mat-icon>
           قائمة الشركاء
          </h1>
        <div *appHasPermission="['Permissions.Partner.Add']">
          <button mat-raised-button color="primary" class="create-button" (click)="createPartner()">
            <mat-icon>add</mat-icon>
            إضافة شريك جديد
          </button>
          </div>
        </div>
      </div>

      <!-- partenrs Table -->
      <mat-card class="table-card">
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="dataSource"  matSort class="partners-table mat-elevation-z8">
              <!-- Name Column -->
              <ng-container  matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>اسـم الشـريك</th>
                <td mat-cell  *matCellDef="let partner">
                 <div class="name-cell">
                <strong> {{ partner.name }} </strong>
                <div class="description" *ngIf="partner.description">
                  {{ partner.description }}
                </div>
              </div>
              </td>

              </ng-container>

              <!-- Description Column -->
              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>الوصـف</th>
                <td mat-cell *matCellDef="let partner">
                  {{ partner.description || '-' }}
                </td>
              </ng-container>

              <!-- initialCapital Column -->
              <ng-container matColumnDef="initialCapital">
                <th mat-header-cell *matHeaderCellDef>الاستثمار الاولي</th>
                <td mat-cell *matCellDef="let partner">
                 {{ partner.initialCapital ? (partner.initialCapital | currency:'EGP':'symbol':'1.0-0') : '-' }}
                </td>
              </ng-container>

              <!-- Created Date Column -->
              <ng-container matColumnDef="createdAt">
                <th mat-header-cell *matHeaderCellDef>تاريـخ الإنشاء</th>
                <td mat-cell *matCellDef="let partner">
                  {{ partner.createdAt | date:'dd/MM/yyyy' }}
                </td>
              </ng-container>

            <ng-container matColumnDef="isDeleted">
              <th mat-header-cell *matHeaderCellDef> الحالة </th>
              <td mat-cell *matCellDef="let element">
                <ng-container *ngIf="element.isDeleted; else activeStatus">
                  <mat-icon class="delete-worm">block</mat-icon> محذوف
                </ng-container>
                <ng-template #activeStatus>
                  <mat-icon class="active-prim">check_circle</mat-icon> نشط
                </ng-template>
              </td>
            </ng-container>
            <div *appHasPermission="['Permissions.Partner.Delete','Permissions.Partner.Edit']">
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>الإجـراءات</th>
                <td mat-cell *matCellDef="let partner">
                  <div class="actions-cell">
                    <div *appHasPermission="['Permissions.Partner.Edit']">
                    <button mat-icon-button color="primary" matTooltip="تعديل"  (click)="editPartner(partner)">
                      <mat-icon>edit</mat-icon>
                     
                    </button>
                    </div>
                    <div *appHasPermission="['Permissions.Partner.Delete']">
                    <button mat-icon-button color="warn" matTooltip="حذف" (click)="deletePartner(partner)">
                      <mat-icon>delete</mat-icon>
                    </button>
                   </div>
                  </div>
                </td>
              </ng-container>
           </div>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <!-- Loading Spinner -->
            <div class="loading-container" *ngIf="loading">
              <mat-spinner diameter="50"></mat-spinner>
              <p class="loading-text">جاري تحميل الشركاء...</p>
            </div>

            <!-- No Data Message -->
            <div class="no-data-container" *ngIf="!loading && Partners.length === 0">
              <mat-icon class="no-data-icon">people</mat-icon>
              <h3 class="no-data-title">لا يوجد شركاء</h3>
              <p class="no-data-message">لم يتم العثور على أي شريك. قم بإضافة شريك جديد للبدء.</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
 </div>
