/* You can add global styles to this file, and also import other style files */

/* @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap'); */
@import 'material-icons/iconfont/material-icons.css';
/* Angular Material Basic Styles */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  src: url('/fonts/roboto/Roboto-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  src: url('/fonts/roboto/Roboto-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  src: url('/fonts/roboto/Roboto-SemiBold.ttf') format('truetype');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  src: url('/fonts/roboto/Roboto-Bold.ttf') format('truetype');
}


@font-face {
  font-family: 'Cairo';
  font-style: normal;
  font-weight: 300;
  src: url('/fonts/cairo/Cairo-Light.ttf') format('truetype');
}

@font-face {
  font-family: 'Cairo';
  font-style: normal;
  font-weight: 400;
  src: url('/fonts/cairo/Cairo-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Cairo';
  font-style: normal;
  font-weight: 500;
  src: url('/fonts/cairo/Cairo-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'Cairo';
  font-style: normal;
  font-weight: 600;
  src: url('/fonts/cairo/Cairo-SemiBold.ttf') format('truetype');
}

@font-face {
  font-family: 'Cairo';
  font-style: normal;
  font-weight: 700;
  src: url('/fonts/cairo/Cairo-Bold.ttf') format('truetype');
}


  

html, body {
  height: 100%;
  direction: rtl;
}

body {
  margin: 0;
  font-family: 'Cairo', 'Roboto', "Helvetica Neue", sans-serif;
  background-color: #f8fafc;
}

/* RTL Support */
.mat-drawer-container {
  direction: rtl;
}

.mat-drawer-content {
  direction: rtl;
}



/* .mat-accent {
  background-color: #10b981 !important;
} */

.mat-warn {
  background-color: #ef4444 !important;
}

/* Success and Error Snackbar Styles */
.success-snackbar {
  background-color: #10b981 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #ef4444 !important;
  color: white !important;
}

/* RTL Support for Material Components */
.mat-mdc-form-field {
  direction: rtl;
  text-align: right; 
  font-family: 'Cairo', 'Roboto', sans-serif !important;

}
.mat-form-field{
  direction: rtl;
    font-family: 'Cairo', 'Roboto', sans-serif !important;
}

.mat-internal-form-field>label{
  font-family: 'Cairo', 'Roboto', sans-serif !important;
}

.mat-select{
  direction: rtl;
  font-family: 'Cairo', 'Roboto', sans-serif !important;
}

.mat-mdc-card {
  direction: rtl;
}

.mat-mdc-button {
  font-family: 'Cairo', 'Roboto', sans-serif !important;
}
.mat-mdc-raised-button{
  font-family: 'Cairo', 'Roboto', sans-serif !important;
  font-size: 1.5rem;
  font-weight: 600 !important;
}
.mat-mdc-raised-button:not(:disabled) {
    color: white !important;
    background-color: #3b82f6 !important;
}

.mat-mdc-raised-button {
  font-family: 'Cairo', 'Roboto', sans-serif !important;
  font-size: 1.5rem;
  font-weight: 600 !important;
  
}
.mat-mdc-snack-bar-container {
  direction: rtl;
  font-family: 'Cairo', 'Roboto', sans-serif !important;
}

.error-snackbar {
  background-color: #ef4444 !important;
  color: white !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.full-width {
  width: 100%;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

/* Card Hover Effects */
.mat-mdc-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

/* Button Styles */
.mat-mdc-raised-button {
  border-radius: 8px;
  font-weight: 500;
}

/* Form Field Styles */
.mat-mdc-form-field {
  direction: rtl;
}

/* Table Styles */
.mat-mdc-table {
  direction: rtl;
}

.mat-mdc-header-cell {
  text-align: right;
  font-weight: 600;
  color: #1e293b;
}

.mat-mdc-cell {
  text-align: right;
}

/* Loading Spinner */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.mat-mdc-header-cell {
  font-size: medium;
font-family: 'Cairo', 'Roboto', sans-serif !important;
}  

.mat-mdc-row {
font-family: 'Cairo', 'Roboto', sans-serif !important;
}
/* Error Message */
.error-message {
  color: #ef4444;
  font-size: 14px;
  margin-top: 8px;
}

/* Success Message */
.success-message {
  color: #10b981;
  font-size: 14px;
  margin-top: 8px;
}

 .mat-tree-overrides{
    background-color: orange;
  }

  .mat-tree-node .mat-tree-node-text-color{
   
    color: red;
  }


.m-0 { margin: 0 !important; }
.m-1 { margin: 8px !important; }
.m-2 { margin: 16px !important; }
.m-3 { margin: 24px !important; }
.m-4 { margin: 32px !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 8px !important; }
.p-2 { padding: 16px !important; }
.p-3 { padding: 24px !important; }
.p-4 { padding: 32px !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 8px !important; }
.mt-2 { margin-top: 16px !important; }
.mt-3 { margin-top: 24px !important; }
.mt-4 { margin-top: 32px !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 8px !important; }
.mb-2 { margin-bottom: 16px !important; }
.mb-3 { margin-bottom: 24px !important; }
.mb-4 { margin-bottom: 32px !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 8px !important; }
.mr-2 { margin-right: 16px !important; }
.mr-3 { margin-right: 24px !important; }
.mr-4 { margin-right: 32px !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 8px !important; }
.ml-2 { margin-left: 16px !important; }
.ml-3 { margin-left: 24px !important; }
.ml-4 { margin-left: 32px !important; }


