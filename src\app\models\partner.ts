export interface Partner { 
  id: number
  name: string
  description: string
  initialCapital: number
  currentCapital: number
  totalInvestments: number
  totalWithdrawals: number
  sharePercentage: any
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
}

export interface PartnerDialogData {
  mode: 'create' | 'edit';
  Partner?: Partner;
}
export interface PartnerForm {
  name: string;
  description: string;
  initialCapital: number;
  isDeleted: boolean;
}

export interface UpdatePartnerDto {
  id: number;
  name: string;
  description: string;
  initialCapital: number;
  isActive: boolean;
}

export interface CreatePartnerDto {
  name: string;
  description: string;
  initialCapital: number;
  isActive: boolean;
}

export interface PartnerTransaction {
  id: number
  transactionDate: Date
  actionDetailId: number
  actionDetailName: string
  partnerId: number
  partnerName: string
  partnerBandId: number
  partnerBandName: string
  amount: number
  description: string
  notes: string
  imagePath: any
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
}

export interface PartnerTransactionReq {
    partnerId: number;
    fromDate: Date;
    toDate: Date;
    bandId: number | null;
}

export interface PartnerBand {
  id: number
  name: string
  description: string
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
}
export interface PartnerBandDialogData {
  mode: 'create' | 'edit';
  partnerBand?: PartnerBand;
}
export interface CreatePartnerBandDto {
  name: string;
  description: string;
}

export interface UpdatePartnerBandDto {
  id: number;
  name: string;
  description: string;
}

export interface CreatePartnerTransactionForm {
  transactionDate: Date;
  actionDetailId: number;
  partnerId: number;
  partnerBandId: number;
  amount: number;
  description: string;
  notes: string;
  imagePath?: File | null; 
}

export interface PartnerTranactionDialogData {
  mode: 'create' | 'edit';
  partnertransaction?: PartnerTransaction;
}

export interface UpdatePartnerTransactionForm {
  id: number;
  transactionDate: Date;
  actionDetailId: number;
  partnerId: number;
  partnerBandId: number;
  amount: number;
  description: string;
  notes?: string;
  imagePath?: File | null; 
}

export interface ShareTransaction {
  transfersDate: Date
  buyerId: number
  sellerId: number
  buyerName: string
  sellerName: string
  sharesCount: number
  transferAmount: number
  description: string
  id: number
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
}

export interface ShareTranactionDialogData {
  mode: 'create' | 'edit';
  sharetransaction?: ShareTransaction;
}

export interface CreateShareTransactionDto {  
  transfersDate: Date;
  buyerId: number;
  sellerId: number;
  sharesCount: number;
  transferAmount: number;
  description: string;
}

export interface UpdateShareTransactionDto {  
  id: number;
  transfersDate: Date;
  buyerId: number;
  sellerId: number;
  sharesCount: number;
  transferAmount: number;
  description: string;
}

export interface PartnerBalanseSummary {
  partnerId: number
  partnerName: string
  initialCapital: number
  totalInvestments: number
  totalWithdrawals: number
  currentCapital: number
  initialShareValue: number
  initialShareCount: number
  initialSharePercentage: number
  totalSharesBuyer: number
  totalSharesSeller: number
  sharePercentage: number
  netShares: number
}