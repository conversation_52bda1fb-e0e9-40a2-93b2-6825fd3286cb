<div class="dialog-container">
  <div mat-dialog-title class="dialog-header">
    <mat-icon>{{ isEditMode ? 'edit' : 'person_add' }}</mat-icon>
    <h2>{{ isEditMode ? 'تعديل البند' : 'إضافة بند جديد' }}</h2>
  </div>

  <div mat-dialog-content class="dialog-content">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="form-row">
        <mat-form-field class="full-width">
          <mat-label>الاسم *</mat-label>
          <input matInput formControlName="name" placeholder="أدخل اسم البند">
          <mat-error *ngIf="form.get('name')?.invalid && form.get('name')?.touched">
            {{ getErrorMessage('name') }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field class="full-width">
          <mat-label>الوصف</mat-label>
          <input matInput formControlName="description" placeholder="الوصف">
          <mat-icon matSuffix>description</mat-icon>
          <mat-error *ngIf="form.get('description')?.invalid && form.get('description')?.touched">
            {{ getErrorMessage('description') }}
          </mat-error>
        </mat-form-field>
      </div>    
          

   
    </form>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-button (click)="onCancel()" [disabled]="loading">
      إلغاء
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="onSubmit()" 
            [disabled]="form.invalid || loading">
      <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
      <mat-icon *ngIf="!loading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
      {{ isEditMode ? 'حفظ التغييرات' : 'إضافة البند' }}
    </button>
  </div>
</div>

