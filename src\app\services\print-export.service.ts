import { Injectable } from '@angular/core';
import { PartnerTransaction } from '../models/partner';



@Injectable({
  providedIn: 'root' 
})
export class PrintExportService {

  constructor() { }
  

   async exportToPDF(partnerTransaction: PartnerTransaction[]): Promise<void> {
    try {
      const html2pdf = (window as any).html2pdf;
      if (!html2pdf) {
        console.error('html2pdf library not loaded');
        throw new Error('مكتبة PDF غير متوفرة');
      }
  const currentDate = new Date().toLocaleTimeString('ar-EG');
      const element = document.createElement('div');
      element.innerHTML = this.generateInvoiceHTML(partnerTransaction, true);
      const opt = { 
        margin: 1,      
        filename: `${currentDate} حركات الشركاء.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
      };

      await html2pdf().set(opt).from(element).save();
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      throw error;
    }
  }


    private generateInvoiceHTML(partnerTransaction: PartnerTransaction[], forPDF: boolean = false): string {
    const items = partnerTransaction || [];
    return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حركات الشركاء</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            background: white;
            color: #333;
            line-height: 1.6;
            ${forPDF ? 'padding: 20px;' : ''}
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            ${!forPDF ? 'box-shadow: 0 0 20px rgba(0,0,0,0.1);' : ''}
            ${!forPDF ? 'border-radius: 10px;' : ''}
            overflow: hidden;
        }

        .invoice-header {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .company-name {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .company-info {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
            background: #f8f9fa;
        }

        .invoice-info, .customer-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2196f3;
            margin-bottom: 15px;
            border-bottom: 2px solid #e3f2fd;
            padding-bottom: 5px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }

        .info-label {
            font-weight: 600;
            color: #666;
        }

        .info-value {
            font-weight: 500;
            color: #333;
        }

        .items-section {
            padding: 30px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .items-table th {
            background: #2196f3;
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
        }

        .items-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .items-table tr:hover {
            background: #e3f2fd;
        }

        .totals-section {
            background: #f8f9fa;
            padding: 30px;
            border-top: 3px solid #2196f3;
        }

        .totals-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            align-items: start;
        }

        .payment-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .totals-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .totals-table tr {
            display: flex;
            justify-content: space-between;
            padding: 12px 20px;
            border-bottom: 1px solid #eee;
        }

        .totals-table tr:last-child {
            background: #2196f3;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            border-bottom: none;
        }

        .footer {
            background: #263238;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9rem;
        }

        .notes {
            background: #fff3e0;
            border-right: 4px solid #ff9800;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }

        .notes-title {
            font-weight: bold;
            color: #ef6c00;
            margin-bottom: 5px;
        }

        @media print {
            body {
                background: white !important;
            }

            .invoice-container {
                box-shadow: none !important;
                border-radius: 0 !important;
            }
        }

        @media (max-width: 768px) {
            .invoice-details {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .totals-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .company-name {
                font-size: 2rem;
            }

            .items-table {
                font-size: 0.9rem;
            }

            .items-table th,
            .items-table td {
                padding: 8px 5px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="company-name">شركة الابواب المصفحة</div>
            <div class="company-info">
                العنوان: شارع التحرير، القاهرة، مصر | الهاتف: 01022207789 | البريد الإلكتروني: <EMAIL>
            </div>
        </div>

    
        <!-- Items Section -->
        <div class="items-section">
            <div class="section-title">تفاصيل الحركات</div>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>م</th>
                        <th>التاريخ</th>
                        <th>الحركة</th>
                        <th>الشريك</th>
                        <th>البند</th>
                        <th>المبلغ</th>
                        <th>البيان</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${items.map((item, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${this.formatDate(item.transactionDate)}</td>
                        <td>${item.actionDetailName}</td>
                        <td>${item.partnerName}</td>
                        <td>${item.partnerBandName}</td>
                        <td>${item.amount.toLocaleString('ar-EG')} جنيه</td>
                        <td>${item.description}</td>
                        <td>${item.notes}</td>
                       
                    </tr>
                    `).join(' ')}
                </tbody>
            </table>
        </div>      
      '}

        <!-- Footer -->
        <div class="footer">
            <p>شركة الابواب المصفحة</p>          
        </div>
    </div>
</body>
</html>
    `;
  }


private formatDate(dateString: string | Date): string {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleDateString('ar-EG', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

}
