import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { inject } from '@angular/core';

export const loginGuard: CanActivateFn = (route, state) => {

const authService = inject(AuthService);
  const router = inject(Router);

 const token = authService.tokenValue;

  // ✅ إذا كان المستخدم لديه توكن صالح، امنعه من الدخول إلى صفحة تسجيل الدخول
  if (token && !authService.isTokenExpired()) {
    router.navigate(['/']);
    return false;
  }

  // ⛔ لا يوجد توكن أو التوكن منتهي الصلاحية → اسمح بالوصول لصفحة login
  return true;
};
