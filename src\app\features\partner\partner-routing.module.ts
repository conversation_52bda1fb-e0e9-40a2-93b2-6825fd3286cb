import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PartnerListComponent } from './component/partner-list/partner-list.component';
import { PartnerListTransactionComponent } from './component/partner-list-transaction/partner-list-transaction.component';
import { BandListComponent } from './component/band-list/band-list.component';
import { ShareTransactionListComponent } from './component/share-transaction-list/share-transaction-list.component';
import { PartnerDashboradComponent } from './component/partner-dashborad/partner-dashborad.component';

const routes: Routes = [
{
    path: 'create',
    component: PartnerListComponent,
    title: 'الشركاء',
  },{
    path: 'transactions-list',    
    component: PartnerListTransactionComponent,
    title: 'معاملات الشركاء',
  }
  ,{
    path: 'band-list',
    component: BandListComponent,
    title: 'بند الشركاء',
  },{
    path: 'share-list',
    component: ShareTransactionListComponent,
    title: 'معاملات الاسهم',
  },{
    path: '',
    component: PartnerDashboradComponent,
    title: 'لوحة تحكم الشركاء',
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PartnerRoutingModule { }
