<div class="dashboard-container">
  <p class="dashboard-title">لوحة معلومات الشريك</p>
  <mat-form-field class="filter-field">
    <mat-label>الشريك</mat-label>
    <mat-select (selectionChange)="onFilterChange($event.value)">
      <mat-option [value]="null">الكل</mat-option>
      <mat-option *ngFor="let rep of partnersList" [value]="rep.id">
        {{ rep.name }}
      </mat-option>
    </mat-select>
  </mat-form-field>

 <button mat-raised-button color="success" class="create-button" (click)="exportToViews()" >
        <mat-icon>picture_as_pdf</mat-icon>
        عرض PDF
  </button>
  <div id="partner-summary-section" *ngIf="partnerBalanseSummaryById; else allPartners">
    <h3>ملخص الشريك</h3>
    <div class="partner-summary">
      <p class="partner-name"><strong>اسم الشريك:</strong> {{ partnerBalanseSummaryById.partnerName }}</p>
     <div class="partner-first">
      <p><strong>الاستثمار الاولي:</strong> {{ partnerBalanseSummaryById.initialCapital }}</p>
      <p><strong>اجمالي الاستثمار:</strong> {{ partnerBalanseSummaryById.totalInvestments }}</p>
      <p><strong>اجمالي السحوبات:</strong> {{ partnerBalanseSummaryById.totalWithdrawals }}</p>
      <p><strong>إجمالي الاستثمار الاولي:</strong> {{ partnerBalanseSummaryById.currentCapital }}</p>
    </div>
     <div class="partner-first-share">
      <p><strong>نسبة الاسهم الابتدائية:</strong> {{ partnerBalanseSummaryById.initialSharePercentage |  number:'1.00' }} %</p>
      <p><strong>القيمة الابتدائية للسهم:</strong> {{ partnerBalanseSummaryById.initialShareValue }}</p>
      <p><strong>عدد الاسهم الابتدائية:</strong> {{ partnerBalanseSummaryById.initialShareCount }}</p>
     </div>
     <div class="partner-last-share">
      <p><strong>عدد الاسهم المباعة:</strong> {{ partnerBalanseSummaryById.totalSharesSeller }}</p>
      <p><strong>عدد الاسهم المشتراة:</strong> {{ partnerBalanseSummaryById.totalSharesBuyer }}</p>
      <p><strong>عدد الاسهم الصافي:</strong> {{ partnerBalanseSummaryById.netShares}}</p>  
      <p><strong>النسبة الحالية:</strong> {{ partnerBalanseSummaryById.sharePercentage }}%</p>
     </div>
    </div>
  </div>


<!-- لو مفيش شريك محدد -->
  <ng-template #allPartners>
    <div class="chart-container">
      <h3>توزيع الأسهم بين الشركاء</h3>
      <canvas
        baseChart
        [data]="{ labels: pieChartLabels, datasets: pieChartDatasets }"
        [type]="pieChartType"
        [options]="pieChartOptions"
        [legend]="true">
      </canvas>
    </div>
  </ng-template>

</div>

