.drawer-container {
  height: calc(100vh - 64px);
  background: #f5f5f5;
}

.filters-drawer {
  width: 320px;
  padding: 16px;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0,0,0,0.1);
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #333;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.categories-section {
  margin-top: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.tree-header {
  padding: 8px 12px;
  background: #f9f9f9;
  border-bottom: 1px solid #e0e0e0;
}

.tree-search-field {
  width: 100%;
  margin-top: 8px;
}

.category-tree {
  padding: 8px 0;
  max-height: 400px;
  overflow-y: auto;
}

.tree-node {
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-name {
  font-size: 14px;
}

.selected {
  font-weight: bold;
  color: #1976d2;
}

.toggle-btn {
  width: 32px;
  height: 32px;
}

.drawer-actions {
  padding: 16px 0;
  margin-top: auto;
}

/* Main Content */
.content {
  padding: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 12px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.5rem;
  color: #333;
}

.action-btn {
  margin-left: 8px;
}

.products-table {
  width: 100%;
  border-collapse: collapse;
}

.mat-column-code, .mat-column-barcode {
  max-width: 100px;
}

.desc {
  font-size: 0.85rem;
  color: #666;
  margin-top: 4px;
}

.status {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.in-stock {
  background: #e8f5e9;
  color: #2e7d32;
}

.low-stock {
  background: #fff8e1;
  color: #c25a00;
}

.out-of-stock {
  background: #ffebee;
  color: #c62828;
}

.actions-cell {
  display: flex;
  gap: 4px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

.no-data mat-icon {
  font-size: 48px;
}

.loading-container {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}