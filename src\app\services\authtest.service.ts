import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { LoginRequest, LoginResponse } from '../models/user.model';
import { Observable, tap } from 'rxjs';
import { ApiResponse } from '../models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class AuthtestService {

  constructor(private apiService: ApiService) { }

  /**
   * Logs in a user with the provided login request.
   * @param loginRequest The login request containing username and password.
   * @returns An observable of the login response.
   */
  login(loginRequest: LoginRequest) : Observable<ApiResponse<LoginResponse>> {
    const endpoint = 'auth/login';
    const body = loginRequest;    
    return this.apiService.post<LoginResponse>(endpoint, body)
      .pipe(
        tap(response => {
          if (response.succeeded && response.data) {
            console.log('Login successful, storing token and user data');
            console.log('User role:', response.data.roles);
            console.log('Token:', response.data.accessToken.substring(0, 20) + '...');
          }
        })
      );    
    }   

  


}
