<div class="layout-container" dir="rtl">
      <!-- Mobile Overlay -->
      <div class="mobile-overlay"
           [class.active]="mobileMenuOpen"
           (click)="closeMobileMenu()"></div>

      <!-- Sidebar -->
      <div class="sidebar"
           [class.collapsed]="sidebarCollapsed"
           [class.mobile-open]="mobileMenuOpen">
        <div class="sidebar-header">
          <h2 *ngIf="!sidebarCollapsed || isMobile">إدارة الشركة</h2>
          <button class="toggle-btn" (click)="toggleSidebar()">
            <span class="hamburger-icon">
              <span></span>
              <span></span>
              <span></span>
            </span>
          </button>
        </div>

        <nav class="nav-list">
          <a routerLink="/app/dashboard" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">📊</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">لوحة التحكم</span>
          </a>
        <div *appHasPermission="['Permissions.Partner.View']">
            <!-- قائمة منسدلة للشركاء -->
          <a href="javascript:void(0)" class="nav-item dropdown-toggle" (click)="toggleMenu('partners')">
            <span class="nav-icon"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FBE6A3"><path d="M40-160v-160q0-34 23.5-57t56.5-23h131q20 0 38 10t29 27q29 39 71.5 61t90.5 22q49 0 91.5-22t70.5-61q13-17 30.5-27t36.5-10h131q34 0 57 23t23 57v160H640v-91q-35 25-75.5 38T480-200q-43 0-84-13.5T320-252v92H40Zm440-160q-38 0-72-17.5T351-386q-17-25-42.5-39.5T253-440q22-37 93-58.5T480-520q63 0 134 21.5t93 58.5q-29 0-55 14.5T609-386q-22 32-56 49t-73 17ZM160-440q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T280-560q0 50-34.5 85T160-440Zm640 0q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T920-560q0 50-34.5 85T800-440ZM480-560q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T600-680q0 50-34.5 85T480-560Z"/></svg></span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">الشركاء</span>
            <span class="dropdown-arrow" [class.open]="isPartnerMenuOpen">
              ▼
            </span>
          </a>
            <!-- العناصر الفرعية للقائمة المنسدلة -->
            <div class="dropdown-menu" *ngIf="isPartnerMenuOpen && (!sidebarCollapsed || isMobile)">
              <a routerLink="/partners" 
              routerLinkActiveOptions="{exact: true}"
              class="nav-item nested" (click)="onNavItemClick()">
                <span class="nav-icon">📄</span>
                <span class="nav-text">الشركاء</span>
              </a>
              <a routerLink="/partners/create" 
              routerLinkActiveOptions="{exact: true}"
              class="nav-item nested" (click)="onNavItemClick()">
                <span class="nav-icon">📄</span>
                <span class="nav-text">إضافة الشركاء</span>
              </a>
              <a routerLink="/partners/transactions-list"  routerLinkActiveOptions="{exact: true}" class="nav-item nested" (click)="onNavItemClick()">
                <span class="nav-icon">📝</span>
                <span class="nav-text">معاملات الشركاء</span>
              </a>
              <a routerLink="/partners/share-list" routerLinkActive="active" class="nav-item nested" (click)="onNavItemClick()">
                <span class="nav-icon"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#255290"><path d="M480 0q-94 0-177.5-33.5t-148-93Q90-186 49-266.5T0-440h80q8 72 38.5 134.5t79 110.5Q246-147 309-117.5T444-82l-62-62 56-56L618-20Q584-9 549.5-4.5T480 0Zm120-200v-80H360q-33 0-56.5-23.5T280-360v-240h-80v-80h80v-80h80v400h400v80h-80v80h-80Zm0-240v-160H440v-80h160q33 0 56.5 23.5T680-600v160h-80Zm280-80q-7-72-38-134.5T762.5-765Q714-813 651-842.5T516-878l62 62-56 56-180-180q34-11 68.5-15.5T480-960q94 0 177.5 33.5t148 93Q870-774 911-693.5T960-520h-80Z"/></svg></span>
                <span class="nav-text">معاملات الاسهم</span>
              </a>
            </div>
        </div>

           <div *appHasPermission="['Permissions.Stores.View']">
            <!-- قائمة منسدلة للمخازن -->
          <a href="javascript:void(0)" class="nav-item dropdown-toggle" (click)="toggleMenu('stores')">
            <!-- <span class="nav-icon">products</span> -->
           <span class="nav-icon"><mat-icon >store</mat-icon></span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">المخازن</span>
            <span class="dropdown-arrow" [class.open]="isPartnerMenuOpen">
              ▼
            </span>
          </a>
            <!-- العناصر الفرعية للقائمة المنسدلة -->
            <div class="dropdown-menu" *ngIf="isStoresMenuOpen && (!sidebarCollapsed || isMobile)">
              <a routerLink="/products" 
              routerLinkActiveOptions="{exact: true}"
              class="nav-item nested" (click)="onNavItemClick()">
                <span class="nav-icon">📄</span>
                <span class="nav-text">المنتجات</span>
              </a>
              <a routerLink="/products/products-list" 
              routerLinkActiveOptions="{exact: true}"
              class="nav-item nested" (click)="onNavItemClick()">
                <span class="nav-icon">📄</span>
                <span class="nav-text">إضافة المنتجات</span>
              </a>
              <a routerLink="/products/category-list"  routerLinkActiveOptions="{exact: true}" class="nav-item nested" (click)="onNavItemClick()">
                <span class="nav-icon">📝</span>
                <span class="nav-text">التصنيفات</span>
              </a>
              <a routerLink="/products/unit-list" routerLinkActive="active" class="nav-item nested" (click)="onNavItemClick()">
                <span class="nav-icon"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#255290"><path d="M480 0q-94 0-177.5-33.5t-148-93Q90-186 49-266.5T0-440h80q8 72 38.5 134.5t79 110.5Q246-147 309-117.5T444-82l-62-62 56-56L618-20Q584-9 549.5-4.5T480 0Zm120-200v-80H360q-33 0-56.5-23.5T280-360v-240h-80v-80h80v-80h80v400h400v80h-80v80h-80Zm0-240v-160H440v-80h160q33 0 56.5 23.5T680-600v160h-80Zm280-80q-7-72-38-134.5T762.5-765Q714-813 651-842.5T516-878l62 62-56 56-180-180q34-11 68.5-15.5T480-960q94 0 177.5 33.5t148 93Q870-774 911-693.5T960-520h-80Z"/></svg></span>
                <span class="nav-text">الوحدات</span>
              </a>
            </div>
        </div>


          <a routerLink="/app/customers" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">👥</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">العملاء</span>
          </a>
          <a routerLink="/app/suppliers" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">🏢</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">الموردين</span>
          </a>
          <a routerLink="/app/products" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">📦</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">المنتجات</span>
          </a>
          <a routerLink="/app/categories" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">🏷️</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">التصنيفات</span>
          </a>

          <a routerLink="/app/units" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">📏</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">الوحدات</span>
          </a>

          <a routerLink="/app/invoices" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">📄</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">الفواتير</span>
          </a>

          <a routerLink="/app/accounts" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">💰</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">الحسابات</span>
          </a>

          <a routerLink="/app/warehouses" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">🏪</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">المخازن</span>
          </a>

          <a routerLink="/app/treasuries" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">🏦</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">الخزائن</span>
          </a>

          <a routerLink="/app/journal-entries" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">📚</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">القيود المحاسبية</span>
          </a>

          <a routerLink="/app/reports" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
            <span class="nav-icon">📈</span>
            <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">التقارير</span>
          </a>

          <div class="nav-divider"></div>

          <div class="admin-section"> 
            <h3 class="section-title" *ngIf="!sidebarCollapsed || isMobile">إدارة النظام</h3>
            <a routerLink="/app/users" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
              <span class="nav-icon">👤</span>
              <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">إدارة المستخدمين</span>
            </a>

            <a routerLink="/app/roles" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
              <span class="nav-icon">🔐</span>
              <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">الأدوار والصلاحيات</span>
            </a>

            <a routerLink="/app/settings" routerLinkActive="active" class="nav-item" (click)="onNavItemClick()">
              <span class="nav-icon">⚙️</span>
              <span class="nav-text" *ngIf="!sidebarCollapsed || isMobile">إعدادات النظام</span>
            </a>
          </div>
        </nav>
      </div>

      <!-- Main Content -->
      <div class="main-container">
        <!-- Top Bar -->
        <div class="top-bar">
          <div class="top-bar-left">
            <button class="menu-btn" (click)="toggleSidebar()">
              <span class="hamburger-icon">
                <span></span>
                <span></span>
                <span></span>
              </span>
            </button>
            <h1 class="page-title">نظام ادارة الابواب المصفحة</h1>
          </div>

          <div class="top-bar-right">
            <div class="user-info" (click)="toggleUserMenu()">        
              <span class="user-name">{{currentUser?.fullName}}</span>
             <div *ngIf="currentUser?.profileImage; else noAvatar" class="user-avatar">
                    <img
                      [src]="currentUser?.profileImage"
                      alt="User Avatar"
                      class="user-cover"
                    />
                  </div>
                  <ng-template #noAvatar>
                    <span class="user-avatar">👤</span>
                  </ng-template>
                          
                      <span class="dropdown-arrow">▼</span>
            </div>

            <div class="user-menu" *ngIf="userMenuOpen">
              <a href="#" class="menu-item" routerLink="/profile">الملف الشخصي</a>
              <a href="#" class="menu-item">الإعدادات</a>
              <div class="menu-divider"></div>
              <a href="#" class="menu-item logout-button" (click)="logout()">تسجيل الخروج</a>
            </div>
          </div>
        </div>

        <!-- Page Content -->
        <div class="page-content">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>

