import { Component, OnInit } from '@angular/core';
import { Partner, PartnerBand, PartnerTransaction } from '../../../../models/partner';
import { PageEvent } from '@angular/material/paginator';
import { PartnerService } from '../../../../services/partner.service';
import { MatDialog } from '@angular/material/dialog';
import { ImagePreviewDialogComponent } from '../../../shared/image-preview-dialog/image-preview-dialog.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { PartnerTransationDialogComponent } from '../partner-transation-dialog/partner-transation-dialog.component';
import { HttpResponse } from '@angular/common/http';
import { response } from 'express';

@Component({
  selector: 'app-partner-list-transaction',
  standalone: false,
  templateUrl: './partner-list-transaction.component.html',
  styleUrl: './partner-list-transaction.component.css'
})
export class PartnerListTransactionComponent implements OnInit {
  partnerTransactions: PartnerTransaction[] = [];
  displayedColumns: string[] = ['id', 'actionDetailName', 'partnerName', 'partnerBandName', 'amount', 'description' , 'notes','imagePath' ,'actions'];
  totalCount = 0;
  pageSize = 10;
  pageNumber = 1;
  searchTerm = '';

  isActive?: boolean; 
  fromDate?:  Date = new Date();
  toDate?: Date = new Date();;
  loading = false;

  selectedPartnerId?: number;
  partners: Partner[] = [];

  selectedbandId?: number;
  bands: PartnerBand[] = [];


constructor(private partnerService: PartnerService,private dialog: MatDialog,private snackBar: MatSnackBar) {
  
}
  ngOnInit(): void {
    this.loadPartnerTransactions();
    this.loadPartners();
    this.loadPartnerBands();
  }

  loadPartners(): void {
    this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.partners = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading representatives:', error);
      }
    });
  }
  loadPartnerBands(): void {
    this.partnerService.getPartnerBands(true).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.bands = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading representatives:', error);
      }
    });
  }

   loadPartnerTransactions(): void {
    this.loading = true;
    this.partnerService.getPartnerTransactions(
      {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        searchTerm: this.searchTerm,
        isActive: this.isActive,
        partnerId: this.selectedPartnerId,
        fromDate: this.fromDate ? this.partnerService.formatDateOnly(this.fromDate) : '',
        toDate: this.toDate ? this.partnerService.formatDateOnly(this.toDate) : '',
        bandId: this.selectedbandId
      },
 
    ).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.partnerTransactions = response.data.items;
          this.totalCount = response.data.totalCount;
        }
        this.loading = false;
      },
      error: (error) => {
         this.loading = false;
          this.partnerTransactions = [];
      }
    });
  }

onPageChange(event: PageEvent): void {
    this.pageNumber = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadPartnerTransactions();
  }

  onSearch(): void {
    this.pageNumber = 1;
    this.loadPartnerTransactions();
  }

  onFilterChange(): void {
    this.pageNumber = 1;
    this.loadPartnerTransactions();
  }



 clearFilters(): void {
    this.searchTerm = '';
    this.selectedPartnerId = undefined;
    this.selectedbandId = undefined;
    this.fromDate = undefined;
    this.toDate = undefined;
    this.pageNumber = 1;
    this.loadPartnerTransactions();
  }


 openImagePreview(imageUrl: string): void {
    this.dialog.open(ImagePreviewDialogComponent, {
      width: 'auto',
      height: 'auto',
      maxWidth: '95vw',
      maxHeight: '95vh',
      data: {
        imageUrl: imageUrl,
        imageAlt: 'صورة الحركة'
      }
    });
  }

createPartnerTransaction(): void {

    const dialogRef = this.dialog.open(PartnerTransationDialogComponent, {
      width: '600px',
      data: { mode: 'create' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadPartnerTransactions();
        this.snackBar.open('تم إنشاء حركات الشريك بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
  }

editPartnerTransaction(partnertransaction: PartnerTransaction): void {
    const dialogRef = this.dialog.open(PartnerTransationDialogComponent, {
      width: '600px',
      data: { mode: 'edit', partnertransaction }
      
    });   
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadPartnerTransactions();
        this.snackBar.open('تم تحديث حركات الشريك بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
  }

deletePartnerTranseaction(partnertransaction: PartnerTransaction): void {
    if (confirm(`هل أنت متأكد من حذف الحركة "${partnertransaction.partnerName}"؟`)) {
      this.partnerService.deletePartnerTransaction(partnertransaction.id).subscribe({
        next: (response) => {
          if (response.succeeded && response.data != null) {
            this.loadPartnerTransactions();
            this.snackBar.open('تم حذف الحركة بنجاح', 'إغلاق', { duration: 3000 });
          }
        },
        error: (error) => {
         
          this.snackBar.open('حدث خطأ أثناء حذف الحركة', 'إغلاق', { duration: 3000 });
        }
      });
    }
  }


exportToViews() { 
    const response = this.partnerService.getPartnerReportTransactionsViewsUrl(
      {
        searchTerm: this.searchTerm,
        isActive: this.isActive,
        partnerId: this.selectedPartnerId,
        fromDate: this.fromDate ? this.partnerService.formatDateOnly(this.fromDate) : '',
        toDate: this.toDate ? this.partnerService.formatDateOnly(this.toDate) : '',
        bandId: this.selectedbandId
      }).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          window.open(response.data, '_blank');
        }
      },
      error: (error) => {
        console.error('Error exporting to PDF:', error);
       // this.snackBar.open('حدث خطأ أثناء عرض PDF', 'إغلاق', { duration: 3000 });
      }
    });    
  }
}


