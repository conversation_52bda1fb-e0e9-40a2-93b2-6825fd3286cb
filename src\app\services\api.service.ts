import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { HandleErrorService } from './handle-error.service';
import { catchError, Observable, retry } from 'rxjs';
import { ApiConfigService } from './api-config.service';
import { ApiResponse } from '../models/api-response.model';
@Injectable({
  providedIn: 'root'
})
export class ApiService {

 private apiUrl: string;

constructor(private http: HttpClient, private handleErrorService: HandleErrorService, private apiConfigService: ApiConfigService) {
 
  this.apiUrl = this.apiConfigService.getApiUrl();
}


// GET request
get<T>(endpoint: string, params?: any): Observable<ApiResponse<T>> {
  return this.http.get<ApiResponse<T>>(`${this.apiUrl}/${endpoint}`, params )
    .pipe(
      retry(2), // Retry the request up to 2 times
      catchError(error => this.handleErrorService.logErrorResponse(error))
    );}


// POST request
post<T>(endpoint: string, body: any): Observable<ApiResponse<T>> {
  return this.http.post<ApiResponse<T>>(`${this.apiUrl}/${endpoint}`, body)
    .pipe(
      retry(2), // Retry the request up to 2 times
      catchError(error => this.handleErrorService.logErrorResponse(error))
    );
  }

// PUT request
put<T>(endpoint: string, body: any): Observable<ApiResponse<T>> {
  return this.http.put<ApiResponse<T>>(`${this.apiUrl}/${endpoint}`, body)
    .pipe(
      retry(2), // Retry the request up to 2 times
      catchError(error => this.handleErrorService.logErrorResponse(error))
    );
  }

// DELETE request
delete<T>(endpoint: string): Observable<ApiResponse<T>> {
  return this.http.delete<ApiResponse<T>>(`${this.apiUrl}/${endpoint}`)
    .pipe(
      retry(2), // Retry the request up to 2 times
      catchError(error => this.handleErrorService.logErrorResponse(error))
    );
  }
// PATCH request
patch<T>(endpoint: string, body: any): Observable<ApiResponse<T>> { 
  return this.http.patch<ApiResponse<T>>(`${this.apiUrl}/${endpoint}`, body)
    .pipe(
      retry(2), // Retry the request up to 2 times
      catchError(error => this.handleErrorService.logErrorResponse(error))
    );
  }



  getView(endpoint: string, params?: any): Observable<ApiResponse<string>> {
  return this.http.get(`${this.apiUrl}/${endpoint}`, params )
    .pipe(
      retry(2), // Retry the request up to 2 times
      catchError(error => this.handleErrorService.logErrorResponse(error))
    );
  } 
    
    
  getURL(endpoint: string): string 
  {
      return `${this.apiUrl}/${endpoint}`;
  }


}
