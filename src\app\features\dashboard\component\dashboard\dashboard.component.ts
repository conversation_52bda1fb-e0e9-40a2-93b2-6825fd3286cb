import { Component, OnInit } from '@angular/core';
import { PartnerService } from '../../../../services/partner.service';
import { PartnerBalanseSummary } from '../../../../models/partner';
import { ChartConfiguration, ChartData, ChartOptions, ChartType } from 'chart.js';

@Component({
  selector: 'app-dashboard',
  standalone: false,
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent implements OnInit {

partners: PartnerBalanseSummary[] = [];

  // بيانات المخطط
  public pieChartType: ChartType = 'pie';
  public pieChartLabels: string[] = [];
  public pieChartDatasets: ChartConfiguration['data']['datasets'] = [
    {
      data: [],
      backgroundColor: [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#C9CBCF', '#7CFC00'
      ],
      hoverBackgroundColor: '#555',
      label: 'نسبة الأسهم'
    }
  ];
public pieChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const label = context.label || '';
            const value = context.parsed;
            return `${label}: ${value.toFixed(2)}%`;
          }
        }
      }
    }
  };
constructor(private partnerService: PartnerService
) {} 


  ngOnInit(): void {
    this.getPartnerBalanseSummary();
  }

  getPartnerBalanseSummary(): void {
    this.partnerService.getPartnersBalanseSummary().subscribe({
      next: (response) => {
         if (response.succeeded && response.data) {
          
        this.partners = response.data;

          // استخراج الأسماء والنسب
          this.pieChartLabels = this.partners.map(p => p.partnerName);
          this.pieChartDatasets[0].data = this.partners.map(p => p.sharePercentage);

          // تحديث المخطط
          this.pieChartDatasets = [...this.pieChartDatasets];
      }
    },     
      error: (error) => {
        console.error('Error loading partners balanse summary:', error);
      }
    });
  }




}
