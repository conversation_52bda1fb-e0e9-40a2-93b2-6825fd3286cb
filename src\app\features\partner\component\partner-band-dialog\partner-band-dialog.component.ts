import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PartnerService } from '../../../../services/partner.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CreatePartnerBandDto, PartnerBand, PartnerBandDialogData, UpdatePartnerBandDto } from '../../../../models/partner';

@Component({
  selector: 'app-partner-band-dialog',
  standalone: false,
  templateUrl: './partner-band-dialog.component.html',
  styleUrl: './partner-band-dialog.component.css'
})
export class PartnerBandDialogComponent implements OnInit {
 
  form: FormGroup;
  loading = false;
  isEditMode: boolean;
  displayedColumns: string[] = ['name', 'description'];

   constructor(
    private fb: FormBuilder,
    private partnerService: PartnerService,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<PartnerBandDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PartnerBandDialogData
  ) {
    this.isEditMode = data.mode === 'edit';
    this.form = this.createForm();
  }

 ngOnInit(): void {
     if (this.isEditMode && this.data.partnerBand) {
      this.populateForm(this.data.partnerBand);
  }
}


   private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(50)]],
      description: ['', [Validators.maxLength(100)]]
    });
  }
 private populateForm(partnerBand: PartnerBand): void {
    this.form.patchValue({
      name: partnerBand.name,
      description : partnerBand.description,
      
    });
  }

 onSubmit(): void {
    if (this.form.valid) {
      this.loading = true;
      const formValue = this.form.value;

      if (this.isEditMode) {
        const updateDto: UpdatePartnerBandDto = formValue;
        this.partnerService.updatePartnerBand(this.data.partnerBand!.id, updateDto)
          .subscribe({
            next: (response) => {
              if (response.succeeded && response.data != null) {
                this.dialogRef.close(true);
              }
              this.loading = false;
            },
            error: (error) => {
                this.loading = false;
            }
          });
      } else {
        const createDto: CreatePartnerBandDto = formValue;
        this.partnerService.createPartnerBand(createDto)
          .subscribe({
            next: (response) => {
              if (response.succeeded && response.data != null) {
                this.snackBar.open('تم إنشاء الشريك بنجاح', 'إغلاق', { duration: 3000 });
                this.dialogRef.close(true);
              }
              this.loading = false;
            },
            error: (error) => {
             
              this.loading = false;
            }
          });
      }
    }
  }




onCancel(): void {
    this.dialogRef.close(false);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field?.hasError('required')) {
      return 'هذا الحقل مطلوب';
    }

    if (field?.hasError('maxlength')) {
      const maxLength = field.errors?.['maxlength']?.requiredLength;
      return `الحد الأقصى ${maxLength} حرف`;
    }
    if (field?.hasError('min')) {
      return 'يجب أن تكون القيمة أكبر من أو تساوي 0';
    }
    if (field?.hasError('max')) {
      return 'يجب أن تكون القيمة أقل من أو تساوي 100';
    }
    return '';
  }


 
}

