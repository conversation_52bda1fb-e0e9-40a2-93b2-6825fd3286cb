import { Component, OnInit } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';
import { Category, Product } from '../../../../models/product.model';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { ProductService } from '../../../../services/product.service';

@Component({
  selector: 'app-product-list',
  standalone: false,
  templateUrl: './product-list.component.html',
  styleUrl: './product-list.component.css'
})
export class ProductListComponent implements OnInit {


dataSourceCategories = new MatTreeNestedDataSource<Category>();
childrenAccessor = (node: Category) => node.children ?? [];

 Products: Product[] = [];  

 selectedCategoryId?: number;
 categories: Category[] = [];  
 
selectedStockStatus?: string;
 stockStatus: string | null = null; 

  totalCount = 0;
  pageSize = 10;
  pageNumber = 1;
  searchTerm = '';

  isActive?: boolean; 
  loading = false;

 displayedColumns: string[] = [ 'code', 'barcode',  'name', 'categoryName' ,'balance', 'actions'];


  constructor(private productService: ProductService) {
  }

  ngOnInit(): void {
    this.loadCategories();
    this.loadData();
  }

 loadCategories(): void {
  this.productService.getCategories().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.categories = response.data;
          this.dataSourceCategories.data = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
 }


loadData() : void{

    this.loading = true;
    this.productService.getProducts(
      {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        searchTerm: this.searchTerm,
        isActive: this.isActive,
        categoryId: this.selectedCategoryId,
        stockStatus: this.selectedStockStatus
      },
 
    ).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.Products = response.data.items;
          this.totalCount = response.data.totalCount;
        }
        this.loading = false;
      },
      error: (error) => {
         this.loading = false;
          this.Products = [];
      }
    });
  }

onPageChange(event: PageEvent): void {
    this.pageNumber = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadData();
  }

  onSearch(): void {
    this.pageNumber = 1;
    this.loadData();
  }

  onFilterChange(): void {
    this.pageNumber = 1;
    this.loadData();
  }



 clearFilters(): void {
    this.searchTerm = '';
    this.selectedCategoryId = undefined;
    this.selectedStockStatus = undefined;
   this.pageNumber = 1;
    this.loadData();
  }



createProduct() { 
}

editProduct(product: Product) { 
}

deleteProduct(product: Product) { 
}

exportToViews () { 
}

selectCategory(categoryId: number) {
  this.selectedCategoryId = categoryId;
  this.onFilterChange();

}
  hasChild = (_: number, node: Category) => node.children && node.children.length > 0;
}


