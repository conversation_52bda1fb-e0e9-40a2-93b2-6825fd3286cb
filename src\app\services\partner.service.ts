import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { CreatePartnerBandDto, CreatePartnerDto, CreatePartnerTransactionForm, CreateShareTransactionDto, Partner, PartnerBalanseSummary, PartnerBand,  PartnerTransaction, ShareTransaction, UpdatePartnerBandDto, UpdatePartnerDto, UpdatePartnerTransactionForm, UpdateShareTransactionDto } from '../models/partner';
import { Observable } from 'rxjs';
import { ApiResponse, PagedResponse } from '../models/api-response.model';
import { HttpParams } from '@angular/common/http';
import { Mainaction } from '../models/mainaction.model';

@Injectable({
  providedIn: 'root'
})
export class PartnerService {

  constructor(private apiService: ApiService) { }

  getPartners(isDelete : boolean = false) : Observable<ApiResponse<Partner[]>> {
    return this.apiService.get<Partner[]>(`Partner?isDelete=${isDelete}`); 
  }
 
  createPartner(partner: CreatePartnerDto) : Observable<ApiResponse<Partner>> {
      return this.apiService.post<Partner>('Partner', partner);
  }

  updatePartner(Id : number, partner: UpdatePartnerDto) : Observable<ApiResponse<Partner>> {
    partner.id = Id;
    return this.apiService.put<Partner>('Partner', partner);
  }
  deletePartner(Id : number) : Observable<ApiResponse<Partner>> {
    return this.apiService.delete<Partner>(`Partner/${Id}`);
  }

  getPartnerBands(isDelete : boolean = false) : Observable<ApiResponse<PartnerBand[]>> {
      return this.apiService.get<PartnerBand[]>(`PartnerBand?isDelete=${isDelete}`); 
  }

  createPartnerBand(partnerBand: CreatePartnerBandDto) : Observable<ApiResponse<PartnerBand>> {
    return this.apiService.post<PartnerBand>('PartnerBand', partnerBand);
  }
  updatePartnerBand(Id : number, partnerBand: UpdatePartnerBandDto) : Observable<ApiResponse<PartnerBand>> {
    partnerBand.id = Id;
    return this.apiService.put<PartnerBand>('PartnerBand', partnerBand);
  }
  deletePartnerBand(Id : number) : Observable<ApiResponse<PartnerBand>> {
    return this.apiService.delete<PartnerBand>(`PartnerBand/${Id}`);
  }

getMainActions(parentActionId : number) : Observable<ApiResponse<Mainaction[]>> {
  return this.apiService.get<Mainaction[]>(`Mainaction/MainActionByAction/${parentActionId}`); 
}

  getPartnerTransactions(paged: any): Observable<ApiResponse<PagedResponse<PartnerTransaction>>> {
  let param = new HttpParams();
   param = param.set('pageNumber', paged.pageNumber ?? 1)
   param = param.set('pageSize', paged.pageSize ?? 10)

   if(paged.searchTerm)
   param = param.set('searchTerm', paged.searchTerm || '') 

   if(paged.isActive != null)
    param = param.set('isActive', paged.isActive)

   if(paged.partnerId)
   param = param.set('partnerId', paged.partnerId)

   if(paged.fromDate)
   param = param.set('fromDate', paged.fromDate)

   if(paged.toDate)
   param = param.set('toDate', paged.toDate)

   if(paged.bandId)
   param = param.set('bandId', paged.bandId);

      return this.apiService.get<PagedResponse<PartnerTransaction>>('Partner/get-all-transactions',   {params:param} );

  }

 createPartnerTransaction(partnertransaction: CreatePartnerTransactionForm) : Observable<ApiResponse<PartnerTransaction>> {
  const formData = new FormData();
  formData.append('TransactionDate', partnertransaction.transactionDate.toDateString());
  formData.append('ActionDetailId', partnertransaction.actionDetailId.toString()); 
  formData.append('PartnerId', partnertransaction.partnerId.toString()); 
  formData.append('PartnerBandId', partnertransaction.partnerBandId.toString()); 
  formData.append('Amount', partnertransaction.amount.toString()); 
  formData.append('Description', partnertransaction.description); 
  formData.append('Notes', partnertransaction.notes); 
  if (partnertransaction.imagePath) {
    formData.append('ImagePath', partnertransaction.imagePath);
  }
  
      return this.apiService.post<PartnerTransaction>('Partner/transactions', formData);
  }

 updatePartnerTransaction(Id : number, partnertransaction: UpdatePartnerTransactionForm) : Observable<ApiResponse<PartnerTransaction>> {
    partnertransaction.id = Id;
   let dateTrans = new Date(partnertransaction.transactionDate).toDateString(); 
    const formData = new FormData();
  formData.append('Id', partnertransaction.id.toString());
  formData.append('TransactionDate', dateTrans);
  formData.append('ActionDetailId', partnertransaction.actionDetailId.toString()); 
  formData.append('PartnerId', partnertransaction.partnerId.toString()); 
  formData.append('PartnerBandId', partnertransaction.partnerBandId.toString()); 
  formData.append('Amount', partnertransaction.amount.toString()); 
  formData.append('Description', partnertransaction.description); 
  formData.append('Notes', partnertransaction.notes || ''); 
  if (partnertransaction.imagePath) {
    formData.append('ImagePath', partnertransaction.imagePath);
  }
    return this.apiService.put<PartnerTransaction>('Partner/transactions',  formData);
  }

  deletePartnerTransaction(id : number) : Observable<ApiResponse<PartnerTransaction>> {
    return this.apiService.delete<PartnerTransaction>(`Partner/transactions/${id}`);
  }
formatDateOnly(date: Date): string {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

 getPartnerReportTransactions(paged: any): Observable<ApiResponse<PartnerTransaction[]>> {
  let param = new HttpParams();
   param = param.set('pageNumber', paged.pageNumber ?? 1)
   param = param.set('pageSize', paged.pageSize ?? 10)

   if(paged.searchTerm)
   param = param.set('searchTerm', paged.searchTerm || '') 

   if(paged.isActive != null)
    param = param.set('isActive', paged.isActive)

   if(paged.partnerId)
   param = param.set('partnerId', paged.partnerId)

   if(paged.fromDate)
   param = param.set('fromDate', paged.fromDate)

   if(paged.toDate)
   param = param.set('toDate', paged.toDate)

   if(paged.bandId)
   param = param.set('bandId', paged.bandId);

      return this.apiService.get<PartnerTransaction[]>('Partner/transactions/report',   {params:param} );

  }


getShareTransactions(paged: any): Observable<ApiResponse<ShareTransaction[]>> {
  let param = new HttpParams();
  
   if(paged.partnerId)
   param = param.set('partnerId', paged.partnerId)

   if(paged.fromDate)
   param = param.set('fromDate', paged.fromDate)

   if(paged.toDate)
   param = param.set('toDate', paged.toDate)

   

      return this.apiService.get<ShareTransaction[]>('Partner/get-all-sharetransfer',   {params:param} );

  }

 createShareTransaction(sharetransaction: CreateShareTransactionDto) : Observable<ApiResponse<ShareTransaction>> {
      return this.apiService.post<ShareTransaction>('Partner/share-transfer', sharetransaction);
  }

  updateShareTransaction(Id : number, sharetransaction: UpdateShareTransactionDto) : Observable<ApiResponse<ShareTransaction>> {
    sharetransaction.id = Id;
    return this.apiService.put<ShareTransaction>('Partner/share-transfer', sharetransaction);
  }
  
  getPartnersBalanseSummary(): Observable<ApiResponse<PartnerBalanseSummary[]>> {
    return this.apiService.get<PartnerBalanseSummary[]>(`Partner/partners-summary`);
  }

 getPartnerReportTransactionsViewsUrl(paged: any): Observable<ApiResponse<string>> {
   let param = new HttpParams()
    .set('pageNumber', paged.pageNumber ?? 1)
    .set('pageSize', paged.pageSize ?? 10);

  if (paged.searchTerm)
    param = param.set('searchTerm', paged.searchTerm || '');

  if (paged.isActive != null)
    param = param.set('isActive', paged.isActive);

  if (paged.partnerId)
    param = param.set('partnerId', paged.partnerId);

  if (paged.fromDate)
    param = param.set('fromDate', paged.fromDate);

  if (paged.toDate)
    param = param.set('toDate', paged.toDate);

  if (paged.bandId)
    param = param.set('bandId', paged.bandId);

  return this.apiService.get('Partner/transactions/reports',   {params:param} );
}

  getPartnerReportSummerViewUrl(): Observable<ApiResponse<string>> {
    return this.apiService.get('Partner/partners-summary/report');
  }

getShareTransactionReportViewUrl(paged: any): Observable<ApiResponse<string>> {
    let param = new HttpParams();
  
   if(paged.partnerId)
   param = param.set('partnerId', paged.partnerId)

   if(paged.fromDate)
   param = param.set('fromDate', paged.fromDate)

   if(paged.toDate)
   param = param.set('toDate', paged.toDate)

  return this.apiService.get('Partner/share/report',   {params:param});
}


}

