import { Component, OnInit } from '@angular/core';
import { Partner, ShareTransaction } from '../../../../models/partner';
import { PartnerService } from '../../../../services/partner.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { ShareTransactionDialogComponent } from '../share-transaction-dialog/share-transaction-dialog.component';

@Component({
  selector: 'app-share-transaction-list',
  standalone: false,
  templateUrl: './share-transaction-list.component.html',
  styleUrl: './share-transaction-list.component.css'
})
export class ShareTransactionListComponent implements OnInit {
  shareTransactions: ShareTransaction[] = [];
   dataSource: MatTableDataSource<ShareTransaction>;
  fromDate?:  Date = new Date();
  toDate?: Date = new Date();;
  loading = false;
 
  selectedPartnerId?: number;
  partners: Partner[] = [];

 displayedColumns: string[] = ['id',  'sellerName', 'buyerName', 'sharesCount', 'transferAmount' , 'description','actions'];

  constructor(
    private partnerService: PartnerService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
      this.dataSource = new MatTableDataSource(this.shareTransactions);
  }
  ngOnInit(): void {
    this.loadShareTransactions();
    this.loadPartners();
  }



loadShareTransactions(): void {
    this.loading = true;
    this.partnerService.getShareTransactions(
      {
        partnerId: this.selectedPartnerId,
        fromDate: this.fromDate ? this.partnerService.formatDateOnly(this.fromDate) : '',
        toDate: this.toDate ? this.partnerService.formatDateOnly(this.toDate) : '',
      },
 
    ).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
            this.shareTransactions = response.data;
          this.dataSource.data = this.shareTransactions;
        }
        this.loading = false;
      },
      error: (error) => {
         this.loading = false;
          this.shareTransactions = [];
      }
    });
  }

 loadPartners(): void {
    this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.partners = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading representatives:', error);
      }
    });
  }




createShareTransaction(): void {

    const dialogRef = this.dialog.open(ShareTransactionDialogComponent, {
         width: '600px',
         data: { mode: 'create' }
       });
   
       dialogRef.afterClosed().subscribe(result => {
         if (result) {
           this.loadShareTransactions();
           this.snackBar.open('تم إنشاء المعاملة بنجاح', 'إغلاق', { duration: 3000 });
         }
       });
  }
editShareTransaction(sharetransaction : ShareTransaction): void {
const dialogRef = this.dialog.open(ShareTransactionDialogComponent, {
      width: '600px',
      data: { mode: 'edit', sharetransaction }
      
    });   
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadShareTransactions();
        this.snackBar.open('تم تحديث المعاملة بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
}
 onFilterChange(): void {
   this.loadShareTransactions();
  }

clearFilters(): void {
    this.selectedPartnerId = undefined;
    this.fromDate = undefined;
    this.toDate = undefined;
    this.loadShareTransactions();
  }

  deleteShareTranseaction(share: ShareTransaction): void {
}

exportToViews() { 
    const response = this.partnerService.getShareTransactionReportViewUrl(
      {
       partnerId: this.selectedPartnerId,
        fromDate: this.fromDate ? this.partnerService.formatDateOnly(this.fromDate) : '',
        toDate: this.toDate ? this.partnerService.formatDateOnly(this.toDate) : '',
      }).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          window.open(response.data, '_blank');
        }
      },
      error: (error) => {
        console.error('Error exporting to PDF:', error);
       // this.snackBar.open('حدث خطأ أثناء عرض PDF', 'إغلاق', { duration: 3000 });
      }
    });    
  }



  }