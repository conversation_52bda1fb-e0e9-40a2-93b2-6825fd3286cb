<mat-drawer-container class="drawer-container" autosize>

  <!-- Sidebar Drawer -->
  <mat-drawer mode="side" opened position="start" class="filters-drawer">

    <div class="drawer-header">
      <h3>تصفية المنتجات</h3>
      <button mat-icon-button (click)="clearFilters()">
        <mat-icon>clear_all</mat-icon>
      </button>
    </div>

    <!-- Search Field -->
    <mat-form-field class="full-width" appearance="outline">
      <mat-label>البحث</mat-label>
      <input matInput
             [(ngModel)]="searchTerm"
             (keyup.enter)="onSearch()"
             placeholder="ابحث بالاسم، الكود أو الباركود">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>

    <!-- Stock Status Filter -->
    <mat-form-field class="full-width" appearance="outline">
      <mat-label>حالة المخزون</mat-label>
      <mat-select [(ngModel)]="selectedStockStatus" (selectionChange)="onFilterChange()">
        <mat-option value="">جميع الحالات</mat-option>
        <mat-option [value]="'instock'">متوفر</mat-option>
        <mat-option [value]="'lowstock'">كمية قليلة</mat-option>
        <mat-option [value]="'outofstock'">غير متوفر</mat-option>
      </mat-select>
    </mat-form-field>

    <!-- Categories Tree -->
    <div class="categories-section">
      <div class="tree-header">
        <mat-label>التصنيفات</mat-label>
        <mat-form-field appearance="outline" class="tree-search-field">
          <input matInput placeholder="ابحث عن تصنيف..." #searchInput (input)="filterCategories(searchInput.value)">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <mat-tree #tree
        [dataSource]="dataSourceCategories"
        [childrenAccessor]="childrenAccessor"
        class="category-tree">
        
        <!-- Leaf Node -->
        <mat-tree-node *matTreeNodeDef="let node" class="tree-node">
          <span class="node-content" (click)="selectCategory(node.id)" [class.selected]="selectedCategoryId === node.id">
            <mat-icon class="icon">label_outline</mat-icon>
            <span class="node-name">{{ node.name }}</span>
          </span>
        </mat-tree-node>

        <!-- Expandable Node -->
        <mat-tree-node *matTreeNodeDef="let node; when: hasChild" class="tree-node expandable">
          <button mat-icon-button matTreeNodeToggle class="toggle-btn">
            <mat-icon>
              {{ tree.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
            </mat-icon>
          </button>
          <span class="node-content" (click)="selectCategory(node.id)" [class.selected]="selectedCategoryId === node.id">
            <mat-icon class="icon">folder_open</mat-icon>
            <span class="node-name">{{ node.name }}</span>
          </span>
        </mat-tree-node>
      </mat-tree>
    </div>

    <!-- Apply Filters Button -->
    <div class="drawer-actions">
      <button mat-raised-button color="primary" (click)="onSearch()" class="full-width">
        <mat-icon>search</mat-icon> تطبيق الفلاتر
      </button>
    </div>

  </mat-drawer>

  <!-- Main Content -->
  <mat-drawer-content class="content">

    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">
        <mat-icon>receipt_long</mat-icon>
        إدارة الأصناف والمنتجات
      </h1>
      <div class="header-actions">
        <button mat-stroked-button color="accent" (click)="exportToViews()" class="action-btn">
          <mat-icon>picture_as_pdf</mat-icon> PDF
        </button>
        <div *appHasPermission="['Permissions.Stores.Add']">
          <button mat-raised-button color="primary" (click)="createProduct()" class="action-btn">
            <mat-icon>add</mat-icon> إنشاء صنف
          </button>
        </div>
      </div>
    </div>

    <!-- Loading Spinner -->
    <div *ngIf="loading" class="loading-container">
      <mat-spinner></mat-spinner>
    </div>

    <!-- Product Table -->
    <mat-card *ngIf="!loading" class="table-card">
      <mat-card-content>
        <div class="table-container">
          <table mat-table [dataSource]="Products" class="products-table">

            <!-- Code -->
            <ng-container matColumnDef="code">
              <th mat-header-cell *matHeaderCellDef>كود الصنف</th>
              <td mat-cell *matCellDef="let prod">{{ prod.code || '-' }}</td>
            </ng-container>

            <!-- Barcode -->
            <ng-container matColumnDef="barcode">
              <th mat-header-cell *matHeaderCellDef>الباركود</th>
              <td mat-cell *matCellDef="let prod">{{ prod.barcode || '-' }}</td>
            </ng-container>

            <!-- Name -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>الاسم</th>
              <td mat-cell *matCellDef="let prod">
                <strong>{{ prod.name }}</strong>
                <p class="desc" *ngIf="prod.description">{{ prod.description }}</p>
              </td>
            </ng-container>

            <!-- Category -->
            <ng-container matColumnDef="categoryName">
              <th mat-header-cell *matHeaderCellDef>التصنيف</th>
              <td mat-cell *matCellDef="let prod">{{ prod.categoryName || 'عام' }}</td>
            </ng-container>

            <!-- Balance & Stock Status -->
            <ng-container matColumnDef="balance">
              <th mat-header-cell *matHeaderCellDef>الرصيد</th>
              <td mat-cell *matCellDef="let prod">
                <strong>{{ prod.balance || 0 }}</strong>
                <br />
                <span class="status"
                      [ngClass]="{
                        'in-stock': prod.balance > prod.minimumStock,
                        'low-stock': prod.balance > 0 && prod.balance <= prod.minimumStock,
                        'out-of-stock': prod.balance === 0
                      }">
                  {{ getStatusText(prod.balance, prod.minimumStock) }}
                </span>
              </td>
            </ng-container>

            <!-- Actions -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
              <td mat-cell *matCellDef="let prod" class="actions-cell">
                <div *appHasPermission="['Permissions.Stores.Edit']">
                  <button mat-icon-button color="primary" (click)="editProduct(prod)" matTooltip="تعديل">
                    <mat-icon>edit</mat-icon>
                  </button>
                </div>
                <div *appHasPermission="['Permissions.Stores.Delete']">
                  <button mat-icon-button color="warn" (click)="deleteProduct(prod)" matTooltip="حذف">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>

          <!-- No Data -->
          <div *ngIf="Products.length === 0" class="no-data">
            <mat-icon color="disabled">inbox</mat-icon>
            <p>لا توجد منتجات مطابقة للبحث</p>
          </div>
        </div>

        <!-- Pagination -->
        <mat-paginator
          *ngIf="totalCount > 0"
          [length]="totalCount"
          [pageSize]="pageSize"
          [pageSizeOptions]="[5, 10, 25, 50]"
          [pageIndex]="pageNumber - 1"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
      </mat-card-content>
    </mat-card>

  </mat-drawer-content>

</mat-drawer-container>