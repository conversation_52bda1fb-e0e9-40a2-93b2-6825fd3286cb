import { Component, OnInit } from '@angular/core';
import { Category, Product } from '../../../../models/product.model';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { ProductService } from '../../../../services/product.service';
import { PageEvent } from '@angular/material/paginator';




@Component({
  selector: 'app-product-dashborad',
  standalone: false,
  templateUrl: './product-dashborad.component.html',
  styleUrl: './product-dashborad.component.css'
})
export class ProductDashboradComponent implements OnInit {


 Products: Product[] = [];  

 selectedCategoryId?: number;
 categories: Category[] = [];  
 
selectedStockStatus?: string;
 stockStatus: string | null = null; 

  totalCount = 0;
  pageSize = 10;
  pageNumber = 1;
  searchTerm = '';

  isActive?: boolean; 
  loading = false;

 displayedColumns: string[] = [ 'code', 'barcode',  'name', 'categoryName' ,'balance', 'actions'];


  constructor(private productService: ProductService) {
  }

  dataSourceCategories = new MatTreeNestedDataSource<Category>();
  childrenAccessor = (node: Category) => node.children ?? [];


    hasChild = (_: number, node: Category) => node.children && node.children.length > 0;


  ngOnInit(): void {
    this.loadCategories();
    this.loadData();
  }
loadData() : void{

    this.loading = true;
    this.productService.getProducts(
      {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        searchTerm: this.searchTerm,
        isActive: this.isActive,
        categoryId: this.selectedCategoryId,
        stockStatus: this.selectedStockStatus
      },
 
    ).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.Products = response.data.items;
          this.totalCount = response.data.totalCount;
        }
        this.loading = false;
      },
      error: (error) => {
         this.loading = false;
          this.Products = [];
      }
    });
  }
  // نسخة أصلية من التصنيفات
allCategories: Category[] = [];

// تصفية الشجرة
filterCategories(searchTerm: string): void {
  if (!searchTerm.trim()) {
    this.dataSourceCategories.data = this.allCategories;
    return;
  }

  const filtered = this.allCategories
    .map(cat => this.filterNode(cat, searchTerm.trim().toLowerCase()))
    .filter(node => node !== null) as Category[];

  this.dataSourceCategories.data = filtered;
}

getStatusText(balance: number, minimumStock: number): string {
  if (balance === 0) return 'غير متوفر';
  if (balance <= minimumStock) return 'كمية قليلة';
  return 'متوفر';
}

filterNode(node: Category, term: string): Category | null {
  const matches = node.name.toLowerCase().includes(term);
  
  const filteredChildren = (node.children?.map(child => this.filterNode(child, term)) || [])
    .filter(child => child !== null) as Category[];

  if (matches || filteredChildren.length > 0) {
    // نعيد الكائن مع تحويل parentCategoryId إلى عدد (أو -1 إذا كان null)
    return {
      ...node,
      parentCategoryId: node.parentCategoryId ?? -1, // أو 0 إذا لم يكن -1 مناسبًا
      children: filteredChildren
    };
  }
  return null;
}

loadCategories(): void {
  this.productService.getCategories().subscribe({
    next: (response) => {
      if (response.succeeded && response.data) {
        this.allCategories = response.data;
        this.dataSourceCategories.data = response.data;
      }
    },
    error: (error) => {
      console.error('Error loading categories:', error);
    }
  });  
}


onPageChange(event: PageEvent): void {
    this.pageNumber = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadData();
  }

  onSearch(): void {
    this.pageNumber = 1;
    this.loadData();
  }

  onFilterChange(): void {
    this.pageNumber = 1;
    this.loadData();
  }



 clearFilters(): void {
    this.searchTerm = '';
    this.selectedCategoryId = undefined;
    this.selectedStockStatus = undefined;
   this.pageNumber = 1;
    this.loadData();
  }



createProduct() { 
}

editProduct(product: Product) { 
}

deleteProduct(product: Product) { 
}

exportToViews () { 
}

selectCategory(categoryId: number) {
  this.selectedCategoryId = categoryId;
  this.onFilterChange();

}

}