
body {
 direction: rtl;
}

.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}



.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
}
  .background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    background-size: 100px 100px;
    animation: float 20s ease-in-out infinite;
  }

  .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
  }


@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}


.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1200px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
}


.company-section {
  text-align: center;
  color: white;
  margin-bottom: 20px;
}
  .company-logo {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
    .logo-icon {
      font-size: 40px;
      width: 40px;
      height: 40px;
      color: white;
    }
   .company-name {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 8px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
   }

   .company-subtitle {
    font-size: 1.25rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 400;
  }

.login-card-container {
  width: 100%;
  max-width: 450px;
}


.login-card {
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

 
}
 .login-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.4);
  }

.login-header {
  background: linear-gradient(135deg, #3f51b5, #5c6bc0);
  color: white;
  padding: 24px;
  margin: 0;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}
    .login-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .header-text {
      flex: 1;
    }
      .login-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: white;
      }

      .login-subtitle {
        font-size: 0.95rem;
        margin: 0;
        opacity: 0.9;
        color: white;
      }
.login-form-content {
  padding: 32px 24px 24px;
}


.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(#f44336, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid rgba(#f44336, 0.2);

  
}
mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.toggle-password-btn{
  margin-left: 8px;
  border: none;
}
 


.login-button {
  height: 56px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  position: relative;
  transition: all 0.3s ease;

  
  }
.login-spinner {
    margin-left: 8px;}

.button-icon {
    margin-left: 8px;
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

.button-icon:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(#3f51b5, 0.4);
  }
.demo-credentials {
  width: 100%;
  max-width: 450px;
}
  .demo-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
    .demo-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      color: #2196f3;
    }
      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
      }
   

    .credential-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;
      color: #555;
    }
      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: #3f51b5;
      }

      span {
        font-size: 0.95rem;
      }
  .login-footer {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  }
  .footer-text {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 400;
  }

    @media (max-width: 768px) {

      .company-name {
        font-size: 2rem;
      }
     .company-subtitle {
        font-size: 1.1rem;
      }
.login-content {
    gap: 24px;
    padding: 16px;
  }

  .company-section {
    margin-bottom: 16px;
  }

  
    .login-header {
      padding: 20px;
    }
     
        .login-icon {
          font-size: 28px;
          width: 28px;
          height: 28px;
        }

        
          .login-title {
            font-size: 1.3rem;
          }

          .login-subtitle {
            font-size: 0.9rem;
          }
      
   .login-form-content {
      padding: 24px 20px 20px;
    }
        }  

    @media (max-width: 480px) {

      .company-name {
        font-size:1.5rem;
      }
        .company-subtitle {
        font-size: 1rem;
      }


 .login-content {
    gap: 20px;
    padding: 12px;
  }

   
    .company-logo {
      width: 60px;
      height: 60px;
    }
      .logo-icon {
        font-size: 30px;
        width: 30px;
        height: 30px;
      }
    
  

  
    .login-header {
      padding: 16px;
    }
      .header-content {
        flex-direction: column;
        text-align: center;
        gap: 12px;
      }
        .login-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }

                  .login-title {
            font-size: 1.2rem;
          }

          .login-subtitle {
            font-size: 0.85rem;
          }
        
      
    

    .login-form-content {
      padding: 20px 16px 16px;
    }
  

  .login-button {
    height: 52px;
    font-size: 1rem;
  }


    }

[dir="rtl"] 

  .login-spinner {
    margin-left: 0;
    margin-right: 8px;
  }

  .button-icon {
    margin-left: 0;
    margin-right: 8px;
  }

  .error-message {
    text-align: right;
  }

  .demo-header {
    text-align: right;
  }

  .credential-item {
    text-align: right;
  }

h1, h2, h3, h4, h5, h6 {
  font-family: 'Cairo', 'Roboto', sans-serif;
  font-weight: 600;
}

p, span, div {
  font-family: 'Cairo', 'Roboto', sans-serif;
}

::ng-deep 

  .mat-form-field {
    width: 100%;
  }
  .error-snackbar {
    background-color: #f44336 !important; /* لون أحمر */
    color: white !important;
  }

  .success-snackbar {
    background-color: #4caf50 !important; /* لون أخضر */
    color: white !important;
  }
.mat-mdc-form-field {
  direction: rtl;
  text-align: right;
}
  .mat-mdc-form-field-subscript-wrapper {
    direction: rtl;
  }

  .mat-mdc-form-field-error {
    text-align: right;
  }


.mat-mdc-input-element {
  text-align: right;
  direction: rtl;
}

.mat-mdc-select {
  text-align: right;
  direction: rtl;
}

 