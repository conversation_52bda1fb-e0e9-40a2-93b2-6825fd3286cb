<div class="Shares-container">
  <!-- Header -->
  <div class="page-header">
    <h1 class="page-title">
      <mat-icon>receipt_long</mat-icon>
      إدارة معاملات الاسهم
    </h1>
   <button mat-raised-button color="success" class="create-button" (click)="exportToViews()" >
        <mat-icon>picture_as_pdf</mat-icon>
        عرض PDF
     </button>
   <div *appHasPermission="['Permissions.Partner.Add']">
     <button mat-raised-button color="primary" class="create-button" (click)="createShareTransaction()">
      <mat-icon>add</mat-icon>
      إنشاء معاملة جديدة
    </button>
  </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-row">
        <!-- <mat-form-field class="search-field">
          <mat-label>البحث</mat-label>
          <input matInput
                 [(ngModel)]="searchTerm"
                 (keyup.enter)="onSearch()"
                 placeholder="البحث بالبيان او الحركة او الملاحظات">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field> -->

        <mat-form-field class="filter-field">
          <mat-label>الشريك</mat-label>
          <mat-select [(ngModel)]="selectedPartnerId" (selectionChange)="onFilterChange()">
            <mat-option [value]="undefined">الكل</mat-option>
            <mat-option *ngFor="let rep of partners" [value]="rep.id">
              {{ rep.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
       
        <mat-form-field class="date-field">
          <mat-label>من تاريخ</mat-label>
          <input matInput [matDatepicker]="fromPicker" [(ngModel)]="fromDate" (dateChange)="onFilterChange()">
          <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
          <mat-datepicker #fromPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field class="date-field">
          <mat-label>إلى تاريخ</mat-label>
          <input matInput [matDatepicker]="toPicker" [(ngModel)]="toDate" (dateChange)="onFilterChange()">
          <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
          <mat-datepicker #toPicker></mat-datepicker>
        </mat-form-field>

        <div class="filter-actions">
          <!-- <button mat-raised-button color="primary" (click)="onSearch()">
            <mat-icon>search</mat-icon>
            بحث
          </button> -->
          <button mat-button (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            مسح الفلاتر
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <!-- Share Table -->
  <mat-card *ngIf="!loading" class="table-card">
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="shareTransactions" class="Shares-table">

          <!-- Invoice Number Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>
              رقم المعاملة
            </th>
            <td mat-cell *matCellDef="let share">
              <div class="invoice-cell">
                <strong>{{ share.id }}</strong>
                <div class="invoice-date">
                  {{ share.transfersDate | date:'dd/MM/yyyy' }}
                </div>
              </div>
            </td>
          </ng-container>
          <!-- sellerName Column -->
          <ng-container matColumnDef="sellerName">
            <th mat-header-cell *matHeaderCellDef>البائع</th>
            <td mat-cell *matCellDef="let share">
              {{ share.sellerName || '-' }}
            </td>
          </ng-container>
           <!-- sellerName Column -->
          <ng-container matColumnDef="buyerName">
            <th mat-header-cell *matHeaderCellDef>المشترى</th>
            <td mat-cell *matCellDef="let share">
              {{ share.buyerName || '-' }}
            </td>
          </ng-container>

         <!-- sharesCount Amount Column -->
          <ng-container matColumnDef="sharesCount">
            <th mat-header-cell *matHeaderCellDef>عدد الأسهم</th>
            <td mat-cell *matCellDef="let share">
              <div class="amount-cell">
                {{ share.sharesCount ? share.sharesCount : '-' }}
              </div>
            </td>
          </ng-container>

          <!-- transferAmount Amount Column -->
          <ng-container matColumnDef="transferAmount">
            <th mat-header-cell *matHeaderCellDef>المبلغ</th>
            <td mat-cell *matCellDef="let share">
              <div class="amount-cell">
                {{ share.transferAmount ? (share.transferAmount | currency:'EGP ':'symbol':'1.2-2') : '-' }}
              </div>
            </td>
          </ng-container>

          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef>البيان</th>
            <td mat-cell *matCellDef="let share">
              {{ share.description || '-' }}
            </td>
          </ng-container>

         
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let share">
              <div class="actions-cell">
                <div *appHasPermission="['Permissions.Partner.Edit']">
                <button mat-icon-button
                        color="primary"
                        matTooltip="تعديل"
                       (click)="editShareTransaction(share)">
                  <mat-icon>edit</mat-icon>
                </button>
                </div>
                <div *appHasPermission="['Permissions.Partner.Delete']">
                <button mat-icon-button
                        color="warn"
                        matTooltip="حذف"
                       (click)="deleteShareTranseaction(share)" >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data Message -->
        <div *ngIf="shareTransactions.length === 0" class="no-data">
          <mat-icon>receipt_long</mat-icon>
          <p>لا توجد معاملات للاسهم</p>
        </div>
      </div>

 
    </mat-card-content>
  </mat-card>
</div>
