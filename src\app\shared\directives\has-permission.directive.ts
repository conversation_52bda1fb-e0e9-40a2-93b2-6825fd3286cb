import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { AuthService } from '../../services/auth.service';

@Directive({
  selector: '[appHasPermission]',
  standalone: false
})
export class HasPermissionDirective {

private hasView = false;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authService: AuthService 
  ) {}

  @Input() set appHasPermission(permission: string | string[]) {
   
    const userPermissions = this.authService.viewUserPermissions(); // أو حسب اسم الدالة

    const hasPermission = Array.isArray(permission)
      ? permission.some(p => userPermissions.permissions.includes(p))
      : userPermissions.permissions.includes(permission);

    if (hasPermission && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasPermission && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }
}
