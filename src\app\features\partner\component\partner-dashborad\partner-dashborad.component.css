

.dashboard-title {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 20px;
  text-align: start;
  padding: 0 20px;
}

/* الحاوية العامة */
.dashboard-container {
  max-width: 1000px;
  margin: 20px auto;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  direction: rtl; /* عشان العربي */
  font-family: 'Cairo', sans-serif;
}

/* الـ Dropdown */
.filter-field {
  width: 300px;
  margin-bottom: 20px;
  display: block;
}

/* ملخص الشريك */
.partner-summary {
  background: #f9f9f9;
  padding: 15px 20px;
  border-radius: 10px;
  border: 1px solid #ddd;
  margin-top: 15px;
}

.partner-summary p {
  margin: 8px 0;
  font-size: 15px;
}

.partner-summary strong {
  color: #333;
}

/* العناوين */
h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #444;
  border-right: 4px solid #36A2EB;
  padding-right: 8px;
}

/* المخطط */
.chart-container {
  margin-top: 20px;
  padding: 20px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #eee;
  text-align: center;
  max-width: 300px;
}

canvas {
  max-width: 100%;
  height: auto !important;
}

.partner-name{
  background-color: #36A2EB;
  color: #000000;
  padding: 10px;
  border-radius: 10px;
  text-align: center;
  margin: 5px 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.partner-first{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
}

.partner-first p{
  background-color: rgb(127, 197, 255);
  flex: 1 1 25%;
  margin: 5px 0;
  padding: 10px;
  border-radius: 10px;
  text-align: center;
}

.partner-first-share{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
}
.partner-first-share p{
  background-color: rgb(136, 240, 1);
  flex: 1 1 33%;
  margin: 5px 0;
  padding: 10px;
  border-radius: 10px;
  text-align: center;
}

.partner-last-share{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
}
.partner-last-share p{
  background-color: rgb(6, 255, 243);
  flex: 1 1 33%;
  margin: 5px 0;
  padding: 10px;
  border-radius: 10px;
  text-align: center;
}

@media print {

 app-layout, 
  .layout-container
  .nav {
    display: none !important;
  }




  


  body * {
    visibility: hidden;

  }

    button {
    display: none !important;
  }
    
  #partner-summary-section, #partner-summary-section * {
    visibility: visible;
  }
  #partner-summary-section {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    left: 0;
  }
}
