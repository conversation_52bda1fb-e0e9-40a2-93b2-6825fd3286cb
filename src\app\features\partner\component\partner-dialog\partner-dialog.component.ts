import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CreatePartnerDto, Partner, PartnerDialogData, UpdatePartnerDto } from '../../../../models/partner';
import { PartnerService } from '../../../../services/partner.service';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-partner-dialog',
  standalone: false,
  templateUrl: './partner-dialog.component.html',
  styleUrl: './partner-dialog.component.css'
})
export class PartnerDialogComponent implements OnInit {
 
  form: FormGroup;
  loading = false;
  isEditMode: boolean;
  displayedColumns: string[] = ['name', 'description', 'initialCapital' ,'isDeleted'];
  isDelete: boolean =false;

   constructor(
    private fb: FormBuilder,
    private partnerService: PartnerService,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<PartnerDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PartnerDialogData
  ) {
    this.isEditMode = data.mode === 'edit';
    this.form = this.createForm();
  }

 ngOnInit(): void {
     if (this.isEditMode && this.data.Partner) {
      this.populateForm(this.data.Partner);
  }
}


   private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(50)]],
      description: ['', [Validators.maxLength(100)]],
      initialCapital: ['', [Validators.required, Validators.min(0)]],
      isDeleted: ['']
    });
  }
 private populateForm(Partner: Partner): void {
    this.form.patchValue({
      name: Partner.name,
      description : Partner.description,
      initialCapital: Partner.initialCapital,
      isDeleted: Partner.isDeleted
      
    });
   
    this.isDelete =  this.form.get("isDeleted")?.value;
  }

 onSubmit(): void {
    if (this.form.valid) {
      this.loading = true;
      const formValue = this.form.value;

      if (this.isEditMode) {
        const updateDto: UpdatePartnerDto = formValue;
        this.partnerService.updatePartner(this.data.Partner!.id, updateDto)
          .subscribe({
            next: (response) => {
              if (response.succeeded && response.data != null) {
                this.dialogRef.close(true);
              }
              this.loading = false;
            },
            error: (error) => {
                this.loading = false;
            }
          });
      } else {
        const createDto: CreatePartnerDto = formValue;
        this.partnerService.createPartner(createDto)
          .subscribe({
            next: (response) => {
              if (response.succeeded && response.data != null) {
                this.snackBar.open('تم إنشاء الشريك بنجاح', 'إغلاق', { duration: 3000 });
                this.dialogRef.close(true);
              }
              this.loading = false;
            },
            error: (error) => {
             
              this.loading = false;
            }
          });
      }
    }
  }




onCancel(): void {
    this.dialogRef.close(false);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field?.hasError('required')) {
      return 'هذا الحقل مطلوب';
    }

    if (field?.hasError('maxlength')) {
      const maxLength = field.errors?.['maxlength']?.requiredLength;
      return `الحد الأقصى ${maxLength} حرف`;
    }
    if (field?.hasError('min')) {
      return 'يجب أن تكون القيمة أكبر من أو تساوي 0';
    }
    if (field?.hasError('max')) {
      return 'يجب أن تكون القيمة أقل من أو تساوي 100';
    }
    return '';
  }


 
}
